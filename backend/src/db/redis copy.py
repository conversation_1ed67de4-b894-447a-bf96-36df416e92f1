from redis.asyncio.cluster import (
    RedisCluster as AsyncRedisCluster,
    ClusterNode as AsyncClusterNode,
)
from redis.cluster import RedisCluster, ClusterNode

from src.config.load_config import load_config

config = load_config(
    [
        "REDIS_HOST",
        "REDIS_MAX_CONNECTIONS",
        "REDIS_PASSWORD",
        "REDIS_PORT",
        "REDIS_PREFIX",
    ]
)


class AsyncRedis:
    def __init__(self):
        self.prefix = config.get("REDIS_PREFIX")
        self.client = AsyncRedisCluster(
            startup_nodes=[
                AsyncClusterNode(host=node_host, port=int(config["REDIS_PORT"]))
                for node_host in config["REDIS_HOST"].split(",")
            ],
            password=config["REDIS_PASSWORD"],
            max_connections=int(config["REDIS_MAX_CONNECTIONS"]),
        )

    async def __del__(self):
        await self.client.close()

    def _add_prefix(self, key):
        if isinstance(key, bytes):
            key = key.decode()
        return f"{self.prefix}:{key}"

    async def set(self, key, value, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.set(key, value, *args, **kwargs)

    async def get(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.get(key, *args, **kwargs)

    async def exists(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.exists(key, *args, **kwargs)


class Redis:
    def __init__(self):
        self.prefix = config.get("REDIS_PREFIX")
        self.client = RedisCluster(
            startup_nodes=[
                ClusterNode(host=node_host, port=int(config["REDIS_PORT"]))
                for node_host in config["REDIS_HOST"].split(",")
            ],
            password=config["REDIS_PASSWORD"],
            max_connections=int(config["REDIS_MAX_CONNECTIONS"]),
        )

    def __del__(self):
        self.client.close()

    def _add_prefix(self, key):
        if isinstance(key, bytes):
            key = key.decode()
        return f"{self.prefix}:{key}"

    def set(self, key, value, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.set(key, value, *args, **kwargs)

    def get(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.get(key, *args, **kwargs)

    def exists(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.exists(key, *args, **kwargs)


__all__ = ["AsyncRedis", "Redis"]
