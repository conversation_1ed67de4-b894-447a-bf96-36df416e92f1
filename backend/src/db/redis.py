import redis
import redis.asyncio as async_redis

from src.config.load_config import load_config

config = {
    "REDIS_HOST": "localhost",
    "REDIS_MAX_CONNECTIONS": 20,
    "REDIS_PORT": "6379",
    "REDIS_PREFIX": "gemini-fullstack-langgraph-quickstart",
}


pool = redis.ConnectionPool().from_url(f"redis://{config['REDIS_HOST']}:{config['REDIS_PORT']}")
async_pool = async_redis.ConnectionPool().from_url(f"redis://{config['REDIS_HOST']}:{config['REDIS_PORT']}")


class AsyncRedis:
    def __init__(self):
        self.prefix = config.get("REDIS_PREFIX")
        self.client = async_redis.Redis(connection_pool=async_pool)

    async def __del__(self):
        await self.client.close()

    def _add_prefix(self, key):
        if isinstance(key, bytes):
            key = key.decode()
        return f"{self.prefix}:{key}"

    async def set(self, key, value, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.set(key, value, *args, **kwargs)

    async def get(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.get(key, *args, **kwargs)

    async def exists(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.exists(key, *args, **kwargs)


class Redis:
    def __init__(self):
        self.prefix = config.get("REDIS_PREFIX")
        self.client = redis.Redis(connection_pool=pool)

    def __del__(self):
        self.client.close()

    def _add_prefix(self, key):
        if isinstance(key, bytes):
            key = key.decode()
        return f"{self.prefix}:{key}"

    def set(self, key, value, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.set(key, value, *args, **kwargs)

    def get(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.get(key, *args, **kwargs)

    def exists(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.exists(key, *args, **kwargs)


__all__ = ["AsyncRedis", "Redis"]
