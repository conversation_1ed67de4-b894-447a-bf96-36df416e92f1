import re
from typing import Dict, Optional, List, Tuple

from loguru import logger


class FileClassifier:
    """文件分类器，根据文件名判断所属知识库"""

    # 知识库ID映射
    DATASET_ID_MAP = {
        "产品培训文档库": "7c30d2c2-9811-46e4-bfa1-2514a041c534",
        "投后管理报告库": "5baef91d-8427-4650-9215-fc9485f686e5",
        "产品生命周期管理库": "5e49f020-b567-4abf-8646-4fc2731f5b69",
        "资产配置策略库": "96bc3e5b-c622-4550-9348-070923f8807c",
        "合规操作运营库": "694cffeb-26c6-4881-bc3e-fe604e3e9f0e",
        "服务信托业务库": "9f4bdad9-dfb5-4b55-bcab-30331067c824",
        "内部工具使用库": "6ec71da5-d1c0-4c98-baf8-1e2dc7a524ac",
    }

    # 各知识库的关键词规则
    CLASSIFICATION_RULES = {
        "产品培训文档库": [
            "推介PPT", "路演PPT", "路演", "预热",
            "推介计划书", "产品介绍", "计划书", "项目介绍", "项目简介", "项目推介",
            "QA", "Q&A", "答疑", "问答", "产品培训", "QA解读", "基金培训", "项目培训", "流程培训", "培训视频",
            "推介材料", "推介资料", "营销素材", "营销活动宣导",
            "一页通",
            "培训材料", "培训课件", "培训文档", "专题讲座",
            "回顾与展望", "策略展望", "市场展望", "年度展望", "市场情况及策略解读",
            "投后交流", "投后沟通", "投后解析", "投后培训", "一页通", "宣传册", "宣传单页", "产品介绍", "项目介绍",
            "尽调报告推介", "可研报告推介", "可行性报告推介版",
            "路演材料", "推介计划书", "产品推介PPT",
            "产品销售录音流程", "产品销售录",
            "信托计划说明书",
            "重点目的信息汇总文件"
        ],
        "投后管理报告库": [
            "周报", "月报", "季报", "年报", "半年度报告", "月度简报", "月度报告", "月度回顾", "每周周报",
            "策略分析", "资配月报", "资产配置",
            "双周评", "双周回顾",
            "周度运作", "运作周报",
            "资产管理总部周报", "资产管理总部简报", "资管部报告",
            "月度运作",
            "季度报告", "年度报告", "季度管理报告",
            "业务管理报告", "业务报告",
            "私募基金年报", "私募基金年度报告",
            "归因分析", "贡献分析", "归因报告",
            "净值周报", "净值报告", "净值公告", "净值数据", "业绩展示", "业绩数据", "近期业绩", "业绩跟踪",
            "投资情况", "投资组合", "投资分析", "投资简介", "投资报告", "策略报告",
            "持仓", "持仓明细", "持仓分析",
            "管理人交流", "管理人汇报", "管理人策略交流", "管理人沟通材料",
            "估值说明", "估值公告",
            "尽调跟踪报告", "尽调报告", "可研报告", "尽调分析报告", "可行性分析报告", "可研及尽调报告",
            "可研及尽职调查报告", "尽职调查及可行性报告",
            "每周市场观点", "每周周报", "项目沟通会议纪要",
            "月度管理报告", "季度管理报告", "项目月度情况说明", "项目季度情况说明",
            "投资运作确认书", "起始运作通知书",
            "分息信息表", "月度派息明细"
        ],
        "产品生命周期管理库": [
            "发行计划", "产品计划", "募集材料", "ppt发行",
            "赎回延期", "延期通知", "日延期",
            "临时开放", "开放公告", "开放日", "开放日公告",
            "浮动管理费", "计提基准", "费率表",
            "期限调整", "调整方案", "期限变更",
            "缴款通知", "缴款书",
            "发行方案", "发行说明", "发案", "营销活动方案", "营销推动方案", "发行补充方案",
            "发行公告", "设立公告", "产品发行要素表", "产品发行及到期要素表",
            "营销激励基准调整", "营销激励", "考核方案",
            "临时开放日公告", "成立公告",
            "缴款通知书", "开放募集公告", "开放募集结束公告", "开放信息表",
            "信托合同", "资管计划合同", "基金合同", "合同编号及投资者名单",
            "提前中止还本付息表", "到期还本付息表", "业绩比较基准表", "业绩比较基准", "比较基准表",
            "收益分配表", "清算分配表", "收益分配表或清算分配表", "收益分配加账户信息表",
            "利率表", "分配表", "产品收支明细", "披露明细",
            "申购赎回业务申请表",
            "专户理财业务客户账户申请表",
            "认购(参与)申请表", "认购（参与）申请表",
            "委托人信息", "信托受益权转让合同",
            "融资方财务分析",
            "身份证、银行卡", "身份证银行卡",
            "财富端产品发行计划一览表"
        ],
        "资产配置策略库": [
            "债券培训", "债券研究", "债券策略",
            "红利培训", "红利策略", "红利投资",
            "大类资产研究", "专题研究", "资产配置模型研究",
            "月跟踪报告", "月度跟踪", "配置跟踪", "跟踪报告", "资产配置报告", "月复盘&月展望", "资产配置月报",
            "热点事件", "事件快评", "事件点评",
            "财富策略周报", "财富策略月报", "产品策略周报", "大类资产配置周报",
            "管理人投后交流点评", "管理人点评", "综合评估", "投后交流点评", "投后点评", "运作分析",
            "管理人反馈信息", "资产配置及观点分享", "管理人观点周报", "管理人市场观点周报", "市场观点周报",
            "市场监测", "市场环境", "环境报告",
            "债市投资展望"
        ],
        "合规操作运营库": [
            "合格投资者", "认定规定",
            "消费者权益", "消保案例",
            "企业微信推广", "企微方案",
            "合规营销", "营销管理",
            "专户理财账户申请表",
            "资产管理计划认购申请表",
            "申购赎回申请表",
            "双录话术模板", "人机双录话术",
            "风险承受能力调查问卷", "风险申明书",
            "财富产品模拟收入及绩效政策表"

        ],
        "服务信托业务库": [
            "服务信托业务", "服务信托营销方案", "家庭信托优惠政策",
            "不动产信托", "地产信托",
            "服务信托流程", "业务流程",
            "KYC表", "保险金信托",
            "双录话术", "录音录像",
            "合同模板", "信托合同", "合同范本",
            "操作手册", "家族信托手册"
        ],
        "内部工具使用库": [
            "操作指引", "理财经理指引",
            "企微指南", "使用指南",
            "财富APP", "APP培训", "APP操作",
            "营销情况及重点工作总结", "营销工作总结", "个人营销经验分享", "营销话术建议",
            "U盘安全", "钓鱼网站", "钓鱼邮件", "病毒", "网络挂马", "网络安全", "电信网络诈骗", "个人信息泄露", "安全宣传",
            "团队协作能力", "团队协作力", "问题的询问技巧"
        ]
    }

    def __init__(self):
        """初始化分类器，编译正则表达式以提高性能"""
        self.compiled_rules = {}
        for dataset_name, keywords in self.CLASSIFICATION_RULES.items():
            # 将关键词转换为正则表达式，忽略大小写
            pattern = "|".join(re.escape(keyword) for keyword in keywords)
            self.compiled_rules[dataset_name] = re.compile(pattern, re.IGNORECASE)

    def classify_file(self, filename: str) -> Tuple[Optional[str], Optional[str]]:
        """
        根据文件名分类到对应的知识库
        
        Args:
            filename: 文件名
            
        Returns:
            (dataset_name, dataset_id) 如果匹配到知识库，否则返回 (None, None)
        """
        # 统计每个知识库的匹配分数
        scores = {}

        for dataset_name, pattern in self.compiled_rules.items():
            matches = pattern.findall(filename)
            if matches:
                # 计算匹配分数（匹配到的关键词数量）
                scores[dataset_name] = len(matches)

        if not scores:
            logger.warning(f"文件 '{filename}' 无法匹配到任何知识库")
            return None, None

        # 选择分数最高的知识库
        best_dataset = max(scores.items(), key=lambda x: x[1])
        dataset_name = best_dataset[0]
        dataset_id = self.DATASET_ID_MAP[dataset_name]

        logger.info(f"文件 '{filename}' 分类到知识库: {dataset_name} (匹配{best_dataset[1]}个关键词)")
        return dataset_name, dataset_id

    def batch_classify(self, filenames: List[str]) -> Dict[str, Tuple[Optional[str], Optional[str]]]:
        """
        批量分类文件
        
        Args:
            filenames: 文件名列表
            
        Returns:
            {filename: (dataset_name, dataset_id)} 的字典
        """
        results = {}
        for filename in filenames:
            results[filename] = self.classify_file(filename)
        return results

    def get_unclassified_files(self, filenames: List[str]) -> List[str]:
        """
        获取无法分类的文件列表
        
        Args:
            filenames: 文件名列表
            
        Returns:
            无法分类的文件名列表
        """
        unclassified = []
        for filename in filenames:
            dataset_name, _ = self.classify_file(filename)
            if dataset_name is None:
                unclassified.append(filename)
        return unclassified
