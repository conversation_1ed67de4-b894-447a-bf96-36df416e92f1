from src.financial_advisor.api.request import request


def query_customer_kyc_info(current_manager: dict, payload: dict):
    response = request(
        "assetAllocation/queryKycInfo",
        {"brokerAccount": current_manager["work_no"], **payload},
        {"Authorization": current_manager["access_token"]},
    )
    kyc_list = response["data"]["kycList"]

    if kyc_list is None:
        return []

    return kyc_list
