# 交易中列表信息
from src.financial_advisor.api.request import request
from src.financial_advisor.vos import CustomerTradeInfoVO

NOTICE_TYPE_MAP = {
    "01": "录入合同",
    "02": "视频面签",
    "03": "签署电子合同",
    "04": "打款",
    "05": "签署电子书",
}


def query_customer_trade_info(current_manager: dict, payload: dict):
    page_no = 1
    page_size = 10
    total_cnt = page_no * (page_size + 1)
    customer_trade_info = []

    while page_no * page_size < total_cnt:
        response = request(
            "sidebar/queryNoticeOfWait",
            {
                "managerNo": current_manager["work_no"],
                "externalUserid": current_manager["external_user_id"],
                "pageNo": page_no,
                "pageSize": page_size,
            },
            {"Authorization": current_manager["access_token"]},
        )
        customer_trade_info.extend(response["data"]["noticeList"] or [])
        total_cnt = int(response["data"]["pageInfo"]["totalNum"] or 0)
        page_no += 1

    return [
        CustomerTradeInfoVO(
            apply_date=customer_trade["ddDate"],
            trade_type=customer_trade["businFlagName"],
            product_name=customer_trade["fundName"],
            product_code=customer_trade["fundCode"],
            trade_amount=customer_trade["balance"],
            trade_status=",".join(
                [NOTICE_TYPE_MAP.get(status) or status for status in (customer_trade["noticeTypeList"] or [])]
            ),
        )
        for customer_trade in customer_trade_info
    ]
