from src.financial_advisor.api.request import request


def query_customer_trust_service(current_manager: dict, payload: dict):
    response = request(
        path="assetAllocation/queryTrustService",
        payload={"externalUserid": current_manager["external_user_id"], **payload},
        ex_headers={"Authorization": current_manager["access_token"]},
    )
    return response["data"]["trustServiceAssetList"]
