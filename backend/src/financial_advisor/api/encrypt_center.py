import base64
import hashlib
import logging
import secrets

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SafetyUtil:
    # Encryption configuration
    DEFAULT_ENCODE = "utf-8"
    SM4_KEY_SIZE = 16  # 128 bits = 16 bytes
    KEY = "5B48C628D79179374C910F3114EB30A4"
    IV = "5B48C628D79179374C910F3114EB30A4"
    
    # RSA configuration
    RSA_KEY_SIZE = 1024
    PRIVATE_KEY = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBANJYB5Fpa29LYWABFHL7IAVrt1NcXHH9utDIf4qQ7OqDUSPAjMp+awq2t1EuTtRWai//G37xOd36itT990Ev03dFlBekQd5wkdk4/dmUzH7tmxpJncMDxt3d7TSPOdq999K6OqY3jRDPmccLVBnI8pkOLmJOsTKCT6NrAh53Y9sTAgMBAAECgYBNKR+1lu9jR0q0NhEzUK3hby8z9DixVDt4pWQsB5fxOzTDobnD+nX32ykKLaubNwwDCVkjywfIR0Uwdu2ByzKN7vk/IyxBqVDtIPd2cJTtiQI/XM+s+8V5lEbK3mpebw3BLBVsVA94LHec0rw2MrvohHxJ1xFUpB1Uw6lgSUiN2QJBAO39FvuIy6VSHNGVUIXi7nei+9PUy2EUykKZ0I/Z7dBekornqIojcjEqh9xTxemp4Op6vF8AYOcTXe+kqKrlRSUCQQDiQ1YZFNq5Py1wSyQp2V3hmH3hZeraQ98tIaD1NqoNGv//YN9FVibVuUWckCWKkEuw8fhiNpaI2IdHZiZdY9XXAkAf2Nh0fI/T5bMZkMOfxwhmVhcg977nlOKkGvU/6sfaNG2jzPBO3ANx9OENaOB5LlR8zEPc6q4M/hh58yznAZtJAkAscX+S86a25nL9cLUMx1aKfpKTWJqV/ujyi317TR5/7oLEXfndO7vrFTZYN3Q4UHmdqxnX6NMO1EFWQ/lbNMYHAkA8rg6uc/IOtX0WELqTMYeqi2QrZaLtrp/S+/jsyHtfeNvnxOXjd1o835hhYM8bTyrrpCUcz7cPnVFsoBmqHwMy"
    PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDSWAeRaWtvS2FgARRy+yAFa7dTXFxx/brQyH+KkOzqg1EjwIzKfmsKtrdRLk7UVmov/xt+8Tnd+orU/fdBL9N3RZQXpEHecJHZOP3ZlMx+7ZsaSZ3DA8bd3e00jznavffSujqmN40Qz5nHC1QZyPKZDi5iTrEygk+jawIed2PbEwIDAQAB"
    
    # Predefined keys (same as Java version)
    PRIVATE_KEY_STR = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAN9UJvXfu/sERZXVSwVXOPTbj3BMA7I2hq+1PFjcEi0k+5gVHK2+O1KIFdKw36kc0FjkJhbqV7dZ4Stu1TLIOSbuyn7pOmXkGQhC6t/rkKxNeumpFr0zgvK9M5otLQFt6mRbpVwPyiRFvfKTJDjbn6GC8eDsrOso6dt3lOU1EEslAgMBAAECgYEAkKi+z9cLua3rsQFHJf94mkq6H+BtKHvfRGNd4qUydOGWws0yFWy6mlwh3qbJSY6reljWP1OuW8qxauPQ36MIOPR7ubJHrCCLS71bB+O2F8reAWQga2PMGCXag1MdQOsBH1FmKUBCkIKeZ/vKj/qKoLxjs7eNBfq3e5J7B09JbqUCQQD9e4cVpMljiJ1/bb7V9pIZiSVZ8yoaHD68N+hOEcjb9uTw8h/TJBApcbvRM5HE19u6qq9og68CNRr5KLFfNF1/AkEA4Yv1k7zLgi2A3IXPyHEqFmJauHsGihiN8JOHer5Z8oFFvPWEuIQWC5NgQIv2jtyYG3vq006DICFK90tFctJxWwJBAN7ySQvmViMvQ6eStSinqDTBhZKp5zvz39HYMoCH7+xYKWEq9IbWz0hEenc0Ll+9o8t42LLKL6F3Z/QBsJAWFK0CQQDU6NKsYJbXTu8KvII1W6X4NWYgKXo12BH9HLbSRmgQRLm5MybTK1TIHpnItJbNqy0nHKXCTKX5KZRjtrlbN45rAkAwf4WMDFSsTgDxCkKxa8TQnL7d5JRx9G1s1dX5kOoYG9UQfg5tHNo+O7GVjYnhKOkdlHnB1RFZsWEFvasqWqon\n"
    PUBLIC_KEY_STR = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDfVCb137v7BEWV1UsFVzj0249wTAOyNoavtTxY3BItJPuYFRytvjtSiBXSsN+pHNBY5CYW6le3WeErbtUyyDkm7sp+6Tpl5BkIQurf65CsTXrpqRa9M4LyvTOaLS0BbepkW6VcD8okRb3ykyQ425+hgvHg7KzrKOnbd5TlNRBLJQIDAQAB"

    @staticmethod
    def create_sm4_key():
        """Generate SM4 key string in hex format (uppercase)"""
        try:
            key = secrets.token_bytes(SafetyUtil.SM4_KEY_SIZE)
            key_hex = key.hex().upper()
            logger.info(f"Generated SM4 key: {key_hex}")
            return key_hex
        except Exception as e:
            logger.error(f"Error generating SM4 key: {e}")
            return ""

    @staticmethod
    def encrypt_cbc(key_hex, iv_hex, data):
        """SM4 encryption using CBC mode"""
        try:
            logger.info(f"Original data: {data}")
            
            # Convert hex strings to bytes
            key_bytes = bytes.fromhex(key_hex)
            iv_bytes = bytes.fromhex(iv_hex)
            data_bytes = data.encode(SafetyUtil.DEFAULT_ENCODE)
            
            # Use AES as SM4 equivalent (SM4 is Chinese standard, AES is more widely supported)
            cipher = Cipher(algorithms.AES(key_bytes), modes.CBC(iv_bytes), backend=default_backend())
            encryptor = cipher.encryptor()
            
            # Add PKCS7 padding
            pad_len = 16 - (len(data_bytes) % 16)
            padded_data = data_bytes + bytes([pad_len] * pad_len)
            
            encrypted = encryptor.update(padded_data) + encryptor.finalize()
            encrypted_hex = encrypted.hex()
            
            logger.info(f"Encrypted data: {encrypted_hex}")
            return encrypted_hex
        except Exception as e:
            logger.error(f"Encryption error: {e}")
            return ""

    @staticmethod
    def decrypt_cbc(key_hex, iv_hex, encrypted_hex):
        """SM4 decryption using CBC mode"""
        try:
            logger.info(f"Encrypted data: {encrypted_hex}")
            
            # Convert hex strings to bytes
            key_bytes = bytes.fromhex(key_hex)
            iv_bytes = bytes.fromhex(iv_hex)
            encrypted_bytes = bytes.fromhex(encrypted_hex)
            
            # Use AES as SM4 equivalent
            cipher = Cipher(algorithms.AES(key_bytes), modes.CBC(iv_bytes), backend=default_backend())
            decryptor = cipher.decryptor()
            
            decrypted_padded = decryptor.update(encrypted_bytes) + decryptor.finalize()
            
            # Remove PKCS7 padding
            pad_len = decrypted_padded[-1]
            decrypted = decrypted_padded[:-pad_len]
            
            decrypted_str = decrypted.decode(SafetyUtil.DEFAULT_ENCODE)
            logger.info(f"Decrypted data: {decrypted_str}")
            return decrypted_str
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            return ""

    @staticmethod
    def create_rsa_keys():
        """Generate RSA public and private key pair"""
        try:
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=SafetyUtil.RSA_KEY_SIZE,
                backend=default_backend()
            )
            public_key = private_key.public_key()
            
            # Serialize keys to PEM format then base64
            private_pem = private_key.private_bytes(
                encoding=serialization.Encoding.DER,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            public_pem = public_key.public_bytes(
                encoding=serialization.Encoding.DER,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            private_key_b64 = base64.b64encode(private_pem).decode()
            public_key_b64 = base64.b64encode(public_pem).decode()
            
            logger.info(f"Private key: {private_key_b64}")
            logger.info(f"Public key: {public_key_b64}")
            
            return {
                SafetyUtil.PUBLIC_KEY: public_key_b64,
                SafetyUtil.PRIVATE_KEY: private_key_b64
            }
        except Exception as e:
            logger.error(f"Error generating RSA keys: {e}")
            return {}

    @staticmethod
    def sign(data, private_key_b64):
        """Sign data with private key using SHA256withRSA"""
        try:
            # Hash the data with SHA-256
            digest = hashlib.sha256(data.encode(SafetyUtil.DEFAULT_ENCODE)).digest()
            
            # Decode private key - handle both DER and PEM formats
            try:
                private_key_bytes = base64.b64decode(private_key_b64)
                private_key = serialization.load_der_private_key(
                    private_key_bytes, password=None, backend=default_backend()
                )
            except ValueError:
                # If DER fails, try PEM format
                private_key = serialization.load_pem_private_key(
                    private_key_b64.encode(), password=None, backend=default_backend()
                )
            
            # Sign the hash
            signature = private_key.sign(
                digest,
                padding.PKCS1v15(),
                hashes.SHA256()
            )
            
            return base64.b64encode(signature).decode()
        except Exception as e:
            logger.error(f"Signing error: {e}")
            return ""


    @staticmethod
    def verify(data, signature_b64, public_key_b64):
        """Verify signature with public key"""
        try:
            # Decode signature and public key
            signature_bytes = base64.b64decode(signature_b64)
            public_key_bytes = base64.b64decode(public_key_b64)
            
            public_key = serialization.load_der_public_key(
                public_key_bytes, backend=default_backend()
            )
            
            # Hash the data with SHA-256
            digest = hashlib.sha256(data.encode(SafetyUtil.DEFAULT_ENCODE)).digest()
            
            # Verify signature
            public_key.verify(
                signature_bytes,
                digest,
                padding.PKCS1v15(),
                hashes.SHA256()
            )
            return True
        except Exception as e:
            logger.error(f"Verification error: {e}")
            return False


def main():
    import requests
    import json

    url = "http://10.103.72.1:8080/river"
    path = "/project/getByCode"

    # Test the functionality
    data = {"projectcode": "","userCode":"SYSTEM","userSystem":"9"}

    # Encrypt
    encrypted_data = SafetyUtil.encrypt_cbc(SafetyUtil.KEY, SafetyUtil.IV, json.dumps(data, ensure_ascii=False))
    logger.info(f"Encrypted: {encrypted_data}")
        
    # Sign
    signature = SafetyUtil.sign(encrypted_data, SafetyUtil.PRIVATE_KEY)
    logger.info(f"Signature: {signature}")

    response = requests.post(url+path, json={
        "data": encrypted_data,
        "sign": signature
    })

    response_data = response.json()

    print("response_data >>>>>", response_data)

    is_valid = SafetyUtil.verify(response_data["data"], response_data["sign"], SafetyUtil.PUBLIC_KEY_STR)

    print("is_valid >>>>>", is_valid)

    decrypted_data = SafetyUtil.decrypt_cbc(SafetyUtil.KEY, SafetyUtil.IV, response_data["data"])

    print("decrypted_data >>>>>", decrypted_data)

if __name__ == "__main__":
    main()
    # SafetyUtil.create_rsa_keys()