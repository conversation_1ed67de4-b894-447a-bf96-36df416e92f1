from datetime import datetime, timedelta

from src.financial_advisor.vos import (
    CustomerBasicInfoVO,
    CustomerHoldInfoVO,
    PortfolioVO,
    ProductVO,
)


# 1. 客户基本信息
customer_info = CustomerBasicInfoVO(
    **{
        "customer_id": "************",
        "customer_crm_no": "************",
        "customer_ecif_no": "************",
        "customer_name": "李华",
        "mobile_phone": "***********",  # 根据需求文档中的示例
        "gender": "0",  # 0-女,1-男
        "customer_age": 30,
        "birthday": "********",
        "email": "<EMAIL>",
        "address": "北京市朝阳区",
        "id_status": "1",  # 已实名
        "id_expires_date": "2030-12-31",
        "bank_card_count": 2,  # 银行卡数量
        "prove_status": "1",  # 是合格投资者 0否1是
        "prove_expires_date": "********",  # 有效期至2026-06-30
        "risk_level_status": "1",  # 已完成风测 0否1是
        "risk_level": "C5",  # 积极型
        "risk_level_name": "积极型",
        "risk_level_expires_date": "********",  # 风测有效期至2025-12-31
        "tax_id_status": "1",  # 是税收居民 0否1是
        "tax_id": "1",  # 已申报 税收居民身份状态: 0-未申报 1-已申报
        "tax_id_name": "已申报",  # 税收居民身份状态: 0-未申报 1-已申报
        "tax_id_expires_date": "********",  # 税收居民身份有效期
        "customer_status": "5",  # 持仓客户 客户状态: 0-注册 1-实名 2-意向 3-流失 4-休眠 5-持仓
        "marital_status": "未婚",  # 婚姻状态
        "nation": "汉族",
        "highest_education": "硕士",
        "last_login_time": "2018-08-01 12:00",
        "total_asset": 2200000.22,  # 总资产 2,200,000元 (220万)
        "total_income": 110300.11,  # 累计收益 (单位:元) 110,300
    }
)


# 2. 客户持仓信息 (4条)
customer_hold_info = [
    CustomerHoldInfoVO(
        **{
            "product_name": "招福宝玖晟1号",
            "product_code": "ZX95001",
            "hold_shares": "500000.0",  # 持有份额 (50万份)
            "hold_assets": "525000.0",  # 持有资产 (52.5万)
            "latest_net_value": "1.05",  # 最新净值
            "latest_net_date": datetime.now().strftime("%Y%m%d"),
            "performance_benchmark": "1.05",
            "product_type": "固收类",
        }
    ),
    CustomerHoldInfoVO(
        **{
            "product_name": "泰康颐养信托",
            "product_code": "TKYY001",
            "hold_shares": "300000.0",
            "hold_assets": "750000.0",
            "latest_net_value": "1.25",
            "latest_net_date": datetime.now().strftime("%Y%m%d"),
            "performance_benchmark": "1.25",
            "product_type": "权益类",
        }
    ),
    CustomerHoldInfoVO(
        **{
            "product_name": "涌信宝稳健收益3期",
            "product_code": "YXB003",
            "hold_shares": "400000.0",
            "hold_assets": "440000.0",
            "latest_net_value": "1.10",
            "latest_net_date": datetime.now().strftime("%Y%m%d"),
            "performance_benchmark": "1.10",
            "product_type": "混合类",
        }
    ),
    CustomerHoldInfoVO(
        **{
            "product_name": "名匠展弘1号",
            "product_code": "MJZH001",
            "hold_shares": "100000.0",
            "hold_assets": "180000.0",
            "latest_net_value": "1.80",
            "latest_net_date": datetime.now().strftime("%Y%m%d"),
            "performance_benchmark": "1.80",
            "product_type": "权益类",
        }
    ),
]


# 3. 客户交易记录 (3条)
customer_trade_info = [
    {
        "trade_date": (datetime.now() - timedelta(days=15)).strftime("%Y%m%d"),
        "biz_type": "申购",
        "product_name": "泰康颐养信托",
        "product_code": "TKYY001",
        "trade_amount": 100000,  # 10万元
        "trade_status": "5",  # 已签约
    },
    {
        "trade_date": (datetime.now() - timedelta(days=30)).strftime("%Y%m%d"),
        "biz_type": "赎回",
        "product_name": "招福宝玖晟3号",
        "product_code": "ZX95003",
        "trade_amount": 50000,  # 5万元
        "trade_status": "5",  # 已签约
    },
    {
        "trade_date": (datetime.now() - timedelta(days=45)).strftime("%Y%m%d"),
        "biz_type": "申购",
        "product_name": "名匠展弘1号",
        "product_code": "MJZH001",
        "trade_amount": 180000,  # 18万元
        "trade_status": "5",  # 已签约
    },
]


# 4. 信托产品信息 (用于测试产品查询和对比)
products = [
    ProductVO(
        **{
            "product_id": "ZX95001",
            "product_name": "招福宝玖晟1号",
            "product_status": "2",
            "risk_level": "中高风险",
            "latest_net_value": "1.2355",
            "latest_net_value_date": "2025-09-02",
            "latest_acc_net_value": "2.3455",
            "income_per_ten_thousand": "1.2323",
            "seven_day_annualized_return": "4.56%",
            "set_up_date": "2022-01-10",
            "performance_benchmark": "沪深300指数",
            "deadline": "3年",
            "deadline_type": "固定期限",
            "min_subscription_amount": "1000",
            "launch_date": "2020-02-01",
            "removal_date": None,
            "open_purchase": "每周一",
            "open_date": "每周一",
            "next_open_date": "2024-06-24",
            "last_month_return": "2.5%",
            "last_year_return": "12.3%",
            "product_tags": "积极｜高收益｜中高风险",
            "project_short_name": "稳成长",
            "project_series": "系列A",
            "project_type": "混合类",
            "project_status": "发行中",
            "project_hot": "1",
            "fee_description": "‌固定管理费‌：按年收取，费率一般为0.3%～0.9%，终身寿险型保险金信托在保险金进入账户前不收取。 ‌‌浮动管理费‌：证券投资类信托可能按盈利部分的20%收取。 ‌税费‌：包括营业税金及附加（按税法规定计提）。 ‌变更手续费‌：如增加受益人、修改合同条款等，每次变更可能收取手续费。",
            "template_code": "A",
        }
    ),
    ProductVO(
        **{
            "product_id": "HX007",
            "product_name": "国投泰康信托鸿鹄7号",
            "product_status": "1",
            "risk_level": "中低风险",
            "latest_net_value": "0.987",
            "latest_net_value_date": "2024-06-19",
            "latest_acc_net_value": "1.876",
            "income_per_ten_thousand": "2.34",
            "seven_day_annualized_return": "6.78%",
            "set_up_date": "2021-12-30",
            "performance_benchmark": "国债指数*100%",
            "deadline": "5年",
            "deadline_type": "固定期限",
            "min_subscription_amount": "5000",
            "launch_date": "2021-06-01",
            "removal_date": None,
            "open_purchase": "每半年",  # 开放申赎
            "open_date": None,
            "next_open_date": "2025-09-25",
            "last_month_return": "-1.2%",
            "last_year_return": "8.9%",
            "product_tags": "债券｜成长｜中低风险",
            "project_short_name": "高债",
            "project_series": "系列B",
            "project_type": "固收类",
            "project_status": "预售中",
            "project_hot": "0",
            "fee_description": "申购费0.5%，管理费1.2%",
            "shareLimitDay": "1",  # 锁定期
            "shareLimitDayUnit": "年", 	# 锁定期单位
            "template_code": "A",
        }
    ),
    ProductVO(
        **{
            "product_id": "YXB003",
            "product_name": "涌信宝稳健收益3期",
            "product_status": "2",
            "risk_level": "高风险",
            "latest_net_value": "1.543",
            "latest_net_value_date": "2024-06-18",
            "latest_acc_net_value": "3.210",
            "income_per_ten_thousand": "3.45",
            "seven_day_annualized_return": "10.12%",
            "set_up_date": "2019-09-20",
            "performance_benchmark": "创业板指数",
            "deadline": "无固定期限",
            "deadline_type": "开放式",
            "min_subscription_amount": "2000",
            "launch_date": "2019-10-01",
            "removal_date": "2024-06-15",
            "open_purchase": "否",
            "open_date": None,
            "next_open_date": None,
            "last_month_return": "5.6%",
            "last_year_return": "25.4%",
            "product_tags": "科技｜创新｜高风险",
            "project_short_name": "科创",
            "project_series": "系列C",
            "project_type": "权益类",
            "project_status": "已售空",
            "project_hot": "1",
            "fee_description": "申购费1.5%，管理费1.5%",
            "template_code": "A",
        }
    ),
    ProductVO(
        **{
            "product_id": "MJZH001",
            "product_name": "名匠展弘1号",
            "product_status": "1",
            "risk_level": "低风险",
            "latest_net_value": "1.102",
            "latest_net_value_date": "2024-06-20",
            "latest_acc_net_value": "1.980",
            "income_per_ten_thousand": "0.98",
            "seven_day_annualized_return": "3.45%",
            "set_up_date": "2018-03-05",
            "performance_benchmark": "国债指数",
            "deadline": "2年",
            "deadline_type": "固定期限",
            "min_subscription_amount": "1000",
            "launch_date": "2018-04-01",
            "removal_date": None,
            "open_purchase": "每月1日",
            "open_date": "每月1日",
            "next_open_date": "2024-07-01",
            "last_month_return": "1.1%",
            "last_year_return": "6.7%",
            "product_tags": "债券｜稳健｜低风险",
            "project_short_name": "稳债",
            "project_series": "系列D",
            "project_type": "固收类",
            "project_status": "发行中",
            "project_hot": "0",
            "fee_description": "申购费0.3%，管理费0.6%",
            "shareLimitDay": "183",  # 锁定期
            "shareLimitDayUnit": "天",  # 锁定期单位
            "template_code": "A",
        }
    ),
]


# 5. 标准化组合信息 (用于测试投顾功能)
portfolios = [
    PortfolioVO(
        portfolio_id="P001",
        portfolio_name="稳健成长组合",
        portfolio_type="混合型",
        portfolio_status="发行中",
        risk_level="3",
        risk_level_name="中等风险",
        first_adjust_date="2022-01-15",
        last_adjust_date="2024-04-10",
        acc_return=0.25,
        last_year_return=0.08,
        max_drawdown=-0.12,
    ),
    PortfolioVO(
        portfolio_id="P002",
        portfolio_name="激进进取组合",
        portfolio_type="股票型",
        portfolio_status="发行中",
        risk_level="5",
        risk_level_name="高风险",
        first_adjust_date="2021-06-01",
        last_adjust_date="2024-03-20",
        acc_return=0.45,
        last_year_return=0.15,
        max_drawdown=-0.25,
    ),
    PortfolioVO(
        portfolio_id="P003",
        portfolio_name="保守稳健组合",
        portfolio_type="债券型",
        portfolio_status="发行中",
        risk_level="1",
        risk_level_name="低风险",
        first_adjust_date="2023-02-10",
        last_adjust_date="2024-05-01",
        acc_return=0.10,
        last_year_return=0.04,
        max_drawdown=-0.05,
    ),
    PortfolioVO(
        portfolio_id="P004",
        portfolio_name="均衡配置组合",
        portfolio_type="混合型",
        portfolio_status="发行中",
        risk_level="2",
        risk_level_name="偏低风险",
        first_adjust_date="2022-09-05",
        last_adjust_date="2024-04-25",
        acc_return=0.18,
        last_year_return=0.07,
        max_drawdown=-0.08,
    ),
]

__all__ = ["customer_info", "customer_hold_info", "products", "portfolios"]
