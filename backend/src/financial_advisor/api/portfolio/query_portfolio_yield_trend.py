from src.financial_advisor.api.request import request


def query_portfolio_yield_trend(current_manager: dict, payload: dict):
    response = request(
        path="assetAllocation/queryHistoricalYieldTrend",
        payload={"brokerAccount": current_manager["work_no"], **payload},
        ex_headers={"Authorization": current_manager["access_token"]},
    )
    data = response["data"]
    return data
