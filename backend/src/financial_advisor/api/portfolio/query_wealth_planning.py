from src.financial_advisor.api.request import async_request


async def async_query_wealth_planning(current_manager: dict, payload: dict):
    page_no = 1
    page_size = 10
    total_cnt = page_no * (page_size + 1)
    wealth_planning_list = []

    while page_no * page_size < total_cnt:
      response = await async_request(
          "assetAllocation/queryWealthPlanningList",
          {
              "externalUserid": current_manager["external_user_id"],
              "workNo": current_manager["work_no"],
              "pageSize": page_size,
              "pageNum": page_no,
              **payload,
          },
          {"Authorization": current_manager["access_token"]},
      )
      data = response["data"]
      wealth_planning_list.extend(data["wealthPlanList"] or [])
      total_cnt = int(data["pageInfo"]["totalNum"] or 0)
      page_no += 1

    return wealth_planning_list
