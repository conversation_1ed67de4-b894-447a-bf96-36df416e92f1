from src.financial_advisor.api.request import async_request, request


def query_net_value_trend(current_manager: dict, payload: dict):
    response = request(
        path="assetAllocation/queryProductNetValueTrend",
        payload=payload,
        ex_headers={"Authorization": current_manager["access_token"]},
    )
    data = response["data"]
    return data


async def async_query_net_value_trend(current_manager: dict, payload: dict):
    response = await async_request(
        path="assetAllocation/queryProductNetValueTrend",
        payload=payload,
        ex_headers={"Authorization": current_manager["access_token"]},
    )
    data = response["data"]
    return data
