from src.financial_advisor.api.request import async_request, request


async def async_query_product_fee_info(current_manager: dict, payload: dict):
    response = await async_request(
        "assetConfig/productQry",
        {
            "brokerAccount": current_manager["broker_account"],
            **payload,
        },
        {"Authorization": current_manager["access_token"]},
    )
    product_list = response["data"]["trustProdBaseList"] or []

    return product_list


def query_product_fee_info(current_manager: dict, payload: dict):
    response = request(
        "assetConfig/productQry",
        {
            "brokerAccount": current_manager["broker_account"],
            **payload,
        },
        {"Authorization": current_manager["access_token"]},
    )
    product_list = response["data"]["trustProdBaseList"] or []

    return product_list
