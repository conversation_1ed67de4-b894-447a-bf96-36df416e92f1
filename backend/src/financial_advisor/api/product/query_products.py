# 侧边栏服务接口-产品列表查询
from typing import Optional

from src.config.load_config import load_config
from src.financial_advisor.api.mock import products
from src.financial_advisor.api.product import async_query_product_fee_info, query_product_fee_info
from src.financial_advisor.api.request import async_request, request
from src.financial_advisor.vos import ProductVO


async def async_query_products(
    current_manager: dict,
    product_name: Optional[str] = None,
    product_code: Optional[str] = None,
):
    payload = {"brokerAccount": current_manager["broker_account"]}
    if product_name is not None and len(product_name) > 0:
        payload["fundName"] = product_name
    if product_code is not None and len(product_code) > 0:
        payload["fundCode"] = product_code

    response = await async_request(
        "sidebar/qryProductList",
        payload,
        {"Authorization": current_manager["access_token"]},
    )
    product_list = response["data"]["list"] or []

    if len(product_list) == 0:
        return product_list

    fundCodes = ",".join([prod["fundCode"] for prod in product_list])
    fee_list = await async_query_product_fee_info(current_manager, {
        "fundCode": fundCodes,
        "pageSize": len(product_list),
        "pageNo": 1,
    })

    for prod in product_list:
        l = [f["freedesc"] for f in fee_list if f["proCode"] == prod["fundCode"]]
        prod["feedesc"] = None if len(l) == 0 else l[0]

    return [
        ProductVO(
            product_id=product_info["fundCode"],
            product_name=product_info["fundName"],
            product_status=product_info["fundStatus"],
            risk_level=product_info["riskLevelName"],
            latest_net_value=product_info["lastNetValue"],
            latest_net_value_date=product_info["lastDdDate"],
            latest_acc_net_value=product_info["lastTotalNetValue"],
            income_per_ten_thousand=product_info["lastIncomeUnit"],
            seven_day_annualized_return=product_info["lastIncomeRation"],
            set_up_date=product_info["steupDate"],
            performance_benchmark=product_info["yield"],
            deadline=product_info["deadLine"],
            deadline_type=product_info["deadlineTypeName"],
            min_subscription_amount=product_info["ominBala"],
            launch_date=product_info["startDate"],
            removal_date=product_info["endDate"],
            open_purchase=product_info["openPurche"],
            open_date=product_info["openDate"],
            next_open_date=product_info["nextOpenDate"],
            last_month_return=product_info["incomeRatioMonth"],
            last_year_return=product_info["incomeRatioYear"],
            product_tags=product_info["prodTags"],
            project_short_name=product_info["shortName"],
            project_series=product_info["projectSeriesName"],
            project_type=product_info["projectTypeName"],
            project_status=product_info["statusName"],
            project_hot=product_info["isHotProject"],
            fee_description=product_info["feedesc"],
            template_code=product_info["templateCode"],
        )
        for product_info in product_list
    ]


def query_products(
    current_manager: dict,
    product_name: Optional[str] = None,
    product_code: Optional[str] = None,
):
    if load_config("MOCK_MODE") == "Y":
        return products

    payload = {"brokerAccount": current_manager["broker_account"]}
    if product_name is not None and len(product_name) > 0:
        payload["fundName"] = product_name
    if product_code is not None and len(product_code) > 0:
        payload["fundCode"] = product_code

    response = request(
        "sidebar/qryProductList",
        payload,
        {"Authorization": current_manager["access_token"]},
    )
    product_list = response["data"]["list"] or []

    if len(product_list) == 0:
        return product_list

    fundCodes = ",".join([prod["fundCode"] for prod in product_list])
    fee_list = query_product_fee_info(current_manager, {
        "fundCode": fundCodes,
        "pageSize": len(product_list),
        "pageNo": 1,
    })

    for prod in product_list:
        l = [f["freedesc"] for f in fee_list if f["proCode"] == prod["fundCode"]]
        prod["feedesc"] = None if len(l) == 0 else l[0]

    return [
        ProductVO(
            product_id=product_info["fundCode"],
            product_name=product_info["fundName"],
            product_status=product_info["fundStatus"],
            risk_level=product_info["riskLevelName"],
            latest_net_value=product_info["lastNetValue"],
            latest_net_value_date=product_info["lastDdDate"],
            latest_acc_net_value=product_info["lastTotalNetValue"],
            income_per_ten_thousand=product_info["lastIncomeUnit"],
            seven_day_annualized_return=product_info["lastIncomeRation"],
            set_up_date=product_info["steupDate"],
            performance_benchmark=product_info["yield"],
            deadline=product_info["deadLine"],
            deadline_type=product_info["deadlineTypeName"],
            min_subscription_amount=product_info["ominBala"],
            launch_date=product_info["startDate"],
            removal_date=product_info["endDate"],
            open_purchase=product_info["openPurche"],
            open_date=product_info["openDate"],
            next_open_date=product_info["nextOpenDate"],
            last_month_return=product_info["incomeRatioMonth"],
            last_year_return=product_info["incomeRatioYear"],
            product_tags=product_info["prodTags"],
            project_short_name=product_info["shortName"],
            project_series=product_info["projectSeriesName"],
            project_type=product_info["projectTypeName"],
            project_status=product_info["statusName"],
            project_hot=product_info["isHotProject"],
            fee_description=product_info["feedesc"],
            template_code=product_info["templateCode"],
        )
        for product_info in product_list
    ]
