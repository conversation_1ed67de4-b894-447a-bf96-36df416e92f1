import aiohttp
import logging
import json
import requests
import urllib.parse
import uuid

from src.config.load_config import load_config
from src.db.mysql import insert_row, insert_row_sync
from src.financial_advisor.api.encrypt import decrypt_aes, encrypt_aes


config = load_config(["REQUEST_AES_KEY", "REQUEST_BASE_URL", "REQUEST_HEADERS_TENANTID"])


def process_request_payload(request_payload: dict) -> str:
    request_payload["sqeNo"] = uuid.uuid4().hex
    payload_str = json.dumps(request_payload, ensure_ascii=False)
    quoted_payload_str = urllib.parse.quote(payload_str)
    encrypted_payload = encrypt_aes(quoted_payload_str, config["REQUEST_AES_KEY"])
    return encrypted_payload


def process_response_data(response_data: str) -> dict:
    decrypted_data = decrypt_aes(response_data, config["REQUEST_AES_KEY"])
    unquoted_data = urllib.parse.unquote(decrypted_data)
    try:
        json_data = json.loads(unquoted_data)
    except json.JSONDecodeError:
        formatted_data = unquoted_data.replace("\n", "\\n").replace("\r", "\\r")
        json_data = json.loads(formatted_data)
    return json_data


def request(path: str, payload: dict, ex_headers: dict[str, str] = {}):
    headers = {
        "Accept": "application/json",
        "Content-type": "application/json",
        "tenantId": config["REQUEST_HEADERS_TENANTID"],
        **ex_headers,
    }
    body = {"encryptData": process_request_payload(payload)}
    response = requests.post(
        url=f"{config['REQUEST_BASE_URL']}/{path}",
        headers=headers,
        json=body,
    )
    response_json = response.json()
    response_data = response_json["data"]

    try:
        insert_row_sync(
            """
            INSERT INTO request_record
            (request_url, request_method, request_body, request_headers, request_response)
            VALUES
            (%s, %s, %s, %s, %s)
            """,
            (
                response.url,
                response.request.method,
                json.dumps(body, ensure_ascii=False),
                json.dumps(headers, ensure_ascii=False),
                response.text,
            ),
        )
    except Exception as e:
        logging.error(f"Error inserting row: {e}")

    if type(response_data) == str:
        logging.error(f"request {path} reponse: {response_json}")
        raise Exception(response_json["msg"])

    if response_data["code"] == "000000":
        return {
            **response_data,
            "data": process_response_data(response_data["data"]),
        }
    else:
        logging.error(f"request {path} reponse: {response_json}")
        raise Exception(response_data["message"] or response_data["data"]["message"])


async def async_request(path: str, payload: dict, ex_headers: dict[str, str] = {}):
    async with aiohttp.ClientSession() as session:
        headers = {
            "Accept": "application/json",
            "Content-type": "application/json",
            "tenantId": config["REQUEST_HEADERS_TENANTID"],
            **ex_headers,
        }
        body = {"encryptData": process_request_payload(payload)}
        async with session.post(
            url=f"{config['REQUEST_BASE_URL']}/{path}",
            headers=headers,
            json=body,
        ) as response:
            response_json = await response.json()
            response_text = await response.text()
            response_data = response_json["data"]

            try:
                await insert_row(
                    """
                    INSERT INTO request_record
                    (request_url, request_method, request_body, request_headers, request_response)
                    VALUES
                    (%s, %s, %s, %s, %s)
                    """,
                    (
                        str(response.url),
                        response.request_info.method,
                        json.dumps(body, ensure_ascii=False),
                        json.dumps(headers, ensure_ascii=False),
                        response_text,
                    ),
                )
            except Exception as e:
                logging.error(f"Error inserting row: {e}")

            if type(response_data) == str:
                logging.error(f"request {path} reponse: {response_json}")
                raise Exception(response_json["msg"])

            if response_data["code"] == "000000":
                return {
                    **response_data,
                    "data": process_response_data(response_data["data"]),
                }
            else:
                logging.error(f"request {path} reponse: {response_json}")
                raise Exception(response_data["message"] or response_data["data"]["message"])


__all__ = ["async_request", "request"]
