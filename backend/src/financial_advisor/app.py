from fastapi import FastAP<PERSON>, APIRouter

from src.financial_advisor.middlewares.encryption import EncryptionMiddleware
from src.financial_advisor.routers.asset_allocation import asset_allocation_router
from src.financial_advisor.routers.auth import financial_advisor_auth_router
from src.financial_advisor.routers.polling import polling_router
from src.routers.guess_you_want_to_ask import guess_you_want_to_ask_router
from src.routers.thread_run_steps import thread_run_steps_router

app = FastAPI()
app.add_middleware(
    EncryptionMiddleware,
)

router = APIRouter(prefix="/v1")

router.include_router(asset_allocation_router)
router.include_router(financial_advisor_auth_router)
router.include_router(polling_router)

router.include_router(guess_you_want_to_ask_router)
router.include_router(thread_run_steps_router)

app.include_router(router)
