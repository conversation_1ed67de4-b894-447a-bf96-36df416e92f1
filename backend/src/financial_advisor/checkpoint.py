import json
import logging
import pymysql
import uuid
from datetime import datetime
from langgraph.store.memory import InMemoryStore
from typing import List, Tuple

from src.config.load_config import load_config

config = load_config(
    [
        "MYSQL_CHARSET",
        "MYSQL_DB",
        "MYSQL_HOST",
        "MYSQL_PASSWORD",
        "MYSQL_PORT",
        "MYSQL_USERNAME",
    ]
)


class ChatStreamManager:
    """
    Manages chat stream messages with persistent storage and in-memory caching.

    This class handles the storage and retrieval of chat messages using both
    an in-memory store for temporary data and MySQL for persistent storage.
    It tracks message chunks and consolidates them when a conversation finishes.

    Attributes:
        store (InMemoryStore): In-memory storage for temporary message chunks
        mysql_conn (pymysql.Connection): MySQL connection
        logger (logging.Logger): Logger instance for this class
    """

    def __init__(self) -> None:
        """
        Initialize the ChatStreamManager with database connections.
        """
        self.logger = logging.getLogger(__name__)
        self.store = InMemoryStore()
        self._init_mysql()

    def _init_mysql(self) -> None:
        """Initialize MySQL connection and create table if needed."""

        try:
            self.mysql_conn = pymysql.connect(
                host=config.get("MYSQL_HOST", ""),
                user=config.get("MYSQL_USERNAME", ""),
                password=config.get("MYSQL_PASSWORD", ""),
                database=config.get("MYSQL_DB", ""),
                port=int(config.get("MYSQL_PORT", 3306)),
                charset=config.get("MYSQL_CHARSET", ""),
            )
            self.logger.info("Successfully connected to MySQL")
            self._create_chat_streams_table()
        except Exception as e:
            self.logger.error(f"Failed to connect to MySQL: {e}")

    def _create_chat_streams_table(self) -> None:
        """Create the chat_streams table if it doesn't exist."""
        try:
            with self.mysql_conn.cursor() as cursor:
                create_table_sql = """
                CREATE TABLE IF NOT EXISTS chat_streams (
                    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
                    thread_id VARCHAR(255) NOT NULL UNIQUE,
                    messages JSON NOT NULL,
                    ts TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                );
                """
                create_index_sql = """
                CREATE INDEX idx_chat_streams_ts ON chat_streams(ts);
                """
                cursor.execute(create_table_sql)
                # cursor.execute(create_index_sql)
                self.mysql_conn.commit()
                self.logger.info("Chat streams table created/verified successfully")
        except Exception as e:
            self.logger.error(f"Failed to create chat_streams table: {e}")
            if self.mysql_conn:
                self.mysql_conn.rollback()

    def process_stream_message(self, thread_id: str, message: str, finish_reason: str) -> bool:
        """
        Process and store a chat stream message chunk.

        This method handles individual message chunks during streaming and consolidates
        them into a complete message when the stream finishes. Messages are stored
        temporarily in memory and permanently in MySQL when complete.

        Args:
            thread_id: Unique identifier for the conversation thread
            message: The message content or chunk to store
            finish_reason: Reason for message completion ("stop", "interrupt", or partial)

        Returns:
            bool: True if message was processed successfully, False otherwise
        """
        if not thread_id or not isinstance(thread_id, str):
            self.logger.warning("Invalid thread_id provided")
            return False

        if not message:
            self.logger.warning("Empty message provided")
            return False

        try:
            store_namespace: Tuple[str, str] = ("messages", thread_id)

            cursor = self.store.get(store_namespace, "cursor")
            current_index = 0

            if cursor is None:
                self.store.put(store_namespace, "cursor", {"index": 0})
            else:
                current_index = int(cursor.value.get("index", 0)) + 1
                self.store.put(store_namespace, "cursor", {"index": current_index})

            self.store.put(store_namespace, f"chunk_{current_index}", message)

            if finish_reason in ("stop", "interrupt"):
                return self._persist_complete_conversation(thread_id, store_namespace, current_index)

            return True

        except Exception as e:
            self.logger.error(f"Error processing stream message for thread {thread_id}: {e}")
            return False

    def _persist_complete_conversation(
        self, thread_id: str, store_namespace: Tuple[str, str], final_index: int
    ) -> bool:
        """
        Persist completed conversation to database.

        Retrieves all message chunks from memory store and saves the complete
        conversation to the configured database for permanent storage.

        Args:
            thread_id: Unique identifier for the conversation thread
            store_namespace: Namespace tuple for accessing stored messages
            final_index: The final chunk index for this conversation

        Returns:
            bool: True if persistence was successful, False otherwise
        """
        try:
            memories = self.store.search(store_namespace, limit=final_index + 2)

            messages: List[str] = []
            for item in memories:
                value = item.dict().get("value", "")
                if value and not isinstance(value, dict):
                    messages.append(str(value))

            if not messages:
                self.logger.warning(f"No messages found for thread {thread_id}")
                return False

            if self.mysql_conn is not None:
                return self._persist_to_mysql(thread_id, messages)
            else:
                self.logger.warning("No database connection available")
                return False

        except Exception as e:
            self.logger.error(f"Error persisting conversation for thread {thread_id}: {e}")
            return False

    def _persist_to_mysql(self, thread_id: str, messages: List[str]) -> bool:
        """Persist conversation to MySQL."""
        try:
            with self.mysql_conn.cursor(cursor=pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT id FROM chat_streams WHERE thread_id = %s", (thread_id,))
                existing_record = cursor.fetchone()

                current_timestamp = datetime.now()
                messages_json = json.dumps(messages)

                if existing_record:
                    cursor.execute(
                        """
                        UPDATE chat_streams 
                        SET messages = %s, ts = %s 
                        WHERE thread_id = %s
                        """,
                        (messages_json, current_timestamp, thread_id),
                    )
                    affected_rows = cursor.rowcount
                    self.mysql_conn.commit()

                    self.logger.info(f"Updated conversation for thread {thread_id}: " f"{affected_rows} rows modified")
                    return affected_rows > 0
                else:
                    conversation_id = uuid.uuid4()
                    cursor.execute(
                        """
                        INSERT INTO chat_streams (id, thread_id, messages, ts) 
                        VALUES (%s, %s, %s, %s)
                        """,
                        (conversation_id, thread_id, messages_json, current_timestamp),
                    )
                    affected_rows = cursor.rowcount
                    self.mysql_conn.commit()

                    self.logger.info(f"Created new conversation with ID: {conversation_id}")
                    return affected_rows > 0

        except Exception as e:
            self.logger.error(f"Error persisting to PostgreSQL: {e}")
            if self.mysql_conn:
                self.mysql_conn.rollback()
            return False

    def close(self) -> None:
        """Close database connections."""
        try:
            if self.mysql_conn is not None:
                self.mysql_conn.close()
                self.logger.info("connection closed")
        except Exception as e:
            self.logger.error(f"Error closing connection: {e}")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - close connections."""
        self.close()


# Global instance for backward compatibility
# TODO: Consider using dependency injection instead of global instance
_default_manager = ChatStreamManager()


def chat_stream_message(thread_id: str, message: str, finish_reason: str) -> bool:
    """
    Legacy function wrapper for backward compatibility.

    Args:
        thread_id: Unique identifier for the conversation thread
        message: The message content to store
        finish_reason: Reason for message completion

    Returns:
        bool: True if message was processed successfully
    """
    try:
        return _default_manager.process_stream_message(thread_id, message, finish_reason)
    except Exception as e:
        logging.error(f"Error processing stream message: {e}")
        return False
