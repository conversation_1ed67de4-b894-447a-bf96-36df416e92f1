from typing_extensions import TypedDict

from src.financial_advisor.models import FinancialPlanningProduct


class AuthSignInDto(TypedDict):
    access_token: str
    broker_account: str
    dept_id_list: list[str]
    user_id: str
    work_no: str
    external_user_id: str


class ThreadIdDto(TypedDict):
    thread_id: str


class CreateAssetAllocationDto(TypedDict):
    assets: list
    match_pool_list: list
    planning_type: str
    products: list
    risk_code: str


class CreatePlanningInfoLinkDto(TypedDict):
    cust_name: str
    blob_id: str
    proposal_name: str
    proposal_type: str
    proposal_id: str


class PaginationDto(TypedDict):
    page_num: int
    page_size: int


class AssetAllocationProductDto(PaginationDto):
    asset_cat_code: str
    common_query: str
    pf_cat_code: str
    pool_id_list: list
    risk_code: str


class AssetAllocationProductNetsDto(TypedDict):
    product_code: str
    pf_cat_code: str
    date_type: str


class AssetAllocationProductReplaceDto(FinancialPlanningProduct):
    pool_id_list: list


class CreatePollingDto(ThreadIdDto):
    data: dict
