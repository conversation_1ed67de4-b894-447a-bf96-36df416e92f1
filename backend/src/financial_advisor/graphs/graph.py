import logging

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
    ToolCall,
)
from langchain_core.runnables import RunnableConfig

# from langgraph.checkpoint.mysql.pymysql import PyMySQLSaver
from langgraph.graph import END, StateGraph
from langgraph.types import Send

# from langgraph.store.mysql.pymysql import PyMySQLStore
from typing import Any

from src.financial_advisor.configuration import Configuration
from src.financial_advisor.graphs.cutomer_graph import customer_graph
from src.financial_advisor.graphs.planning_graph import planning_graph
from src.financial_advisor.graphs.portfolio_graph import portfolio_graph
from src.financial_advisor.graphs.product_graph import product_graph
from src.financial_advisor.graphs.search_graph import search_graph
from src.financial_advisor.models import RouteDecision
from src.financial_advisor.prompts import (
    CHAT_PROMPT,
    DATASET_PROMPT,
    get_current_date,
    PROHIBITED_WORDS_PROMPT,
    ROUTING_PROMPT,
)
from src.financial_advisor.state import State
from src.financial_advisor.tools.dify_tools.retrieve_dataset import retrieve_dataset
from src.utils.history_summariser import (
    add_to_state,
    create_history_context,
    get_latest_user_message,
    truncate_history,
)
from src.utils.llm_utils import create_llm, execute_tool_calls


logging.basicConfig(level=logging.DEBUG)


def continue_to_next_node(state: State) -> str | Send:
    if "next_node" in state and state["next_node"] is not None:
        logging.info(f"Continuing to next node: {state['next_node']}")
        return Send(state["next_node"], state["next_state"])
    return "truncate_history"


def system_init(state: State):
    state = add_to_state(state)
    messages = state.get("messages", [])
    system_msg = SystemMessage(
        content=PROHIBITED_WORDS_PROMPT.format(current_date=get_current_date())
    )
    return {
        "messages": [system_msg] + messages,
        "current_step": "系统初始化",
        "current_step_detail": "加载并设置系统提示词",
    }


def general_discussion(state: State, config: RunnableConfig) -> dict[str, Any]:
    user_question = get_latest_user_message(state["messages"])
    if not user_question:
        return {
            "messages": [
                AIMessage(content="我是您的智能理财助手，专注服务信托理财经理")
            ]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "general_chat_model", temperature=0.7)
    history_context = create_history_context(
        state["conversation_history"], llm, max_history_tokens=1500
    )

    messages: list[BaseMessage] = [SystemMessage(content=CHAT_PROMPT)]
    if history_context:
        messages.append(
            SystemMessage(content=f"Previous conversation:\n{history_context}")
        )
    messages.append(HumanMessage(content=user_question))

    response = llm.invoke(messages)

    return {
        "messages": [response],
        "current_step": "普通对话处理",
        "current_step_detail": "基于历史上下文生成智能回复",
    }


def dataset(state: State, config: RunnableConfig) -> dict[str, Any]:
    latest_message = get_latest_user_message(state["messages"])

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1000
        )
        or "No previous conversation"
    )
    formatted_prompt = DATASET_PROMPT.format(
        user_question=latest_message, history_context=history_context
    )
    dataset_tools = [retrieve_dataset]
    llm_with_tools = llm.bind_tools(dataset_tools)
    messages: list[AIMessage | HumanMessage] = [HumanMessage(content=formatted_prompt)]
    ai_msg: AIMessage = llm_with_tools.invoke(messages)
    messages.append(ai_msg)
    messages, card_list = execute_tool_calls(ai_msg, dataset_tools, messages)

    if len(card_list) == 0:
        return {
            **state,
            "current_step": "知识库召回",
            "current_step_detail": "未找到相关数据，尝试调用乐享搜索接口",
            "next_node": "search",
            "next_state": {
                **state,
                "search_query": latest_message,
            },
        }

    if ai_msg.tool_calls and len(ai_msg.tool_calls) > 0:
        tool_call = ai_msg.tool_calls[0]
        final_response = llm.invoke(messages)
        return {
            "messages": [
                AIMessage(
                    content=final_response.content,
                    tool_calls=(
                        [
                            ToolCall(
                                id=tool_call.get("id"),
                                name=tool_call.get("name"),
                                args=card_list[0],
                            )
                        ]
                    ),
                )
            ],
            "current_step": "知识库召回",
            "current_step_detail": f"""召回{tool_call.get("args").get("dataset_name")}并返回结果""",
            "next_node": None,
            "next_state": None,
        }

    return {"messages": [ai_msg]}


def route_query(state: State, config: RunnableConfig) -> dict[str, Any]:
    latest_message = get_latest_user_message(state["messages"])

    if not latest_message:
        logging.warning("No user message found, defaulting to general_discussion")
        return {"query_type": "general_discussion"}

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "router_model", temperature=0.3)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1000
        )
        or "No previous conversation"
    )
    structured_llm = llm.with_structured_output(RouteDecision)

    try:
        decision = structured_llm.invoke(
            ROUTING_PROMPT.format(query=latest_message, history_context=history_context)
        )
        query_type = decision.route
        reasoning = decision.reasoning

        logging.info(f"Query: '{latest_message[:100]}...'")
        logging.info(f"Routed to: {query_type} (confidence: {decision.confidence})")
        logging.info(f"Reasoning: {reasoning}")

        # Validate route name
        valid_routes = [
            "customer_info",
            "dataset",
            "general_discussion",
            "product_info",
            "portfolio_info",
            "financial_planning",
        ]
        if query_type not in valid_routes:
            logging.warning(
                f"Invalid route '{query_type}', defaulting to general_discussion"
            )
            query_type = "general_discussion"

    except Exception as e:
        logging.error(
            f"Error in structured LLM routing: {e}, falling back to general_discussion"
        )
        query_type = "general_discussion"
        reasoning = "未正确识别用户意图，已作为一般对话回复"

    return {
        "query_type": query_type,
        "current_step": "意图识别",
        "current_step_detail": reasoning,
    }


# from src.config.load_config import load_config
# from langgraph.checkpoint.mysql.pymysql import PyMySQLSaver

# checkpoint_url = load_config("MYSQL_URI")
# with PyMySQLSaver.from_conn_string(checkpoint_url) as checkpointer:
#     checkpointer.setup()
#     # graph.checkpointer = checkpointer
graph = (
    StateGraph(State)
    .add_node("system_init", system_init)
    .add_node("route_query", route_query)
    .add_node("truncate_history", truncate_history)
    .add_node("general_discussion", general_discussion)
    .add_node("dataset", dataset)
    .add_node("search", search_graph())
    .add_node("customer_info", customer_graph())
    .add_node("product_info", product_graph())
    .add_node("portfolio_info", portfolio_graph())
    .add_node("financial_planning", planning_graph())
    .set_entry_point("system_init")
    .add_edge("system_init", "route_query")
    .add_conditional_edges(
        "route_query",
        lambda state: state.get("query_type", "general_discussion"),
        {
            "customer_info": "customer_info",
            "dataset": "dataset",
            "general_discussion": "general_discussion",
            "product_info": "product_info",
            "portfolio_info": "portfolio_info",
            "financial_planning": "financial_planning",
        },
    )
    .add_conditional_edges(
        "product_info",
        continue_to_next_node,
        ["dataset", "truncate_history"],
    )
    .add_conditional_edges(
        "dataset",
        continue_to_next_node,
        ["search", "truncate_history"],
    )
    .add_edge("general_discussion", "truncate_history")
    .add_edge("customer_info", "truncate_history")
    .add_edge("portfolio_info", "truncate_history")
    .add_edge("search", "truncate_history")
    .add_edge("financial_planning", "truncate_history")
    .add_edge("truncate_history", END)
    .compile(name="financial_advisor")
)