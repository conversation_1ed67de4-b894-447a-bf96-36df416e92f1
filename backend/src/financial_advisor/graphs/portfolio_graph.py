import json
import logging

from langchain_core.messages import AIMessage, HumanMessage, ToolCall
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.types import Command, interrupt
from typing import Any
from uuid import uuid4

from src.db.redis import Redis
from src.financial_advisor.api.portfolio import (
    generate_portfolio_info_link_id,
    query_stardard_portfolios,
)
from src.financial_advisor.configuration import Configuration
from src.financial_advisor.models import PorfolioRouteDecision, ProductRecommendPitch
from src.financial_advisor.prompts import (
    CUSTOMER_BASIC_INFO_PROMPT,
    CUSTOMER_HOLD_INFO_PROMPT,
    CUSTOMER_PROFILE_PROMPT,
    ENDORSE_PORTFOLIOS_PROMPT,
    PORTFOLIO_BASIC_INFO,
    PORTFOLIO_ROUTING_PROMPT,
)
from src.financial_advisor.state import PortfolioState
from src.financial_advisor.tools.customer_tools import (
    fetch_customer_basic_info,
    fetch_customer_hold_info,
)
from src.financial_advisor.tools.portfolio_tools import fetch_portfolio_detail_info
from src.financial_advisor.utils import extractAuthUserInfoFromRunnableConfig
from src.utils.history_summariser import (
    add_to_state,
    create_history_context,
    get_latest_user_message,
)
from src.utils.llm_utils import create_llm, execute_tool_calls


logging.basicConfig(level=logging.DEBUG)
redis = Redis()


def portfolio_route_query(state: PortfolioState, config: RunnableConfig) -> dict[str, Any] | Command[str]:
    state = add_to_state(state)
    latest_user_message = get_latest_user_message(state.get("messages"))

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "router_model", temperature=0.3)
    history_context = (
        create_history_context(state["conversation_history"], llm, max_history_tokens=1000)
        or "No previous conversation"
    )
    structured_llm = llm.with_structured_output(PorfolioRouteDecision)

    try:
        decision = structured_llm.invoke(
            PORTFOLIO_ROUTING_PROMPT.format(query=latest_user_message, history_context=history_context)
        )
        portfolio_query_type = decision.route

        logging.info(f"Query: '{latest_user_message[:100]}...'")
        logging.info(f"Routed to: {portfolio_query_type} (confidence: {decision.confidence})")
        logging.info(f"Reasoning: {decision.reasoning}")

        # Validate route name
        valid_routes = [
            "analyze_portfolio",
            "endorse_portfolio_for_customer",
        ]
        if portfolio_query_type not in valid_routes:
            logging.warning(f"Invalid route '{portfolio_query_type}', defaulting to general_discussion")
            return Command(goto="general_discussion", graph=Command.PARENT)

    except Exception as e:
        logging.error(f"Error in structured LLM routing: {e}, falling back to general_discussion")
        return Command(goto="general_discussion", graph=Command.PARENT)

    return {"portfolio_query_type": portfolio_query_type}


def analyze_portfolio(state: PortfolioState, config: RunnableConfig):
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {"messages": [AIMessage(content="I couldn't find a question to answer.")]}

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    history_context = (
        create_history_context(state["conversation_history"], llm, max_history_tokens=1500)
        or "No previous financial discussions."
    )

    tools = [fetch_portfolio_detail_info]
    llm_with_tools = llm.bind_tools(tools)
    messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=PORTFOLIO_BASIC_INFO.format(user_question=user_question, history_context=history_context))
    ]
    ai_msg: AIMessage = llm_with_tools.invoke(messages)
    messages.append(ai_msg)
    messages, card_list = execute_tool_calls(ai_msg, tools, messages)

    if len(card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关组合的具体信息")],
            "current_step": "查询组合信息",
            "current_step_detail": f"""查询组合基本信息数据""",
        }

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return {"messages": [AIMessage(content="未能提取用户认证信息")]}

    link_id_data = generate_portfolio_info_link_id(user_info, card_list[0]["card_data"])
    key = f"""polling:{user_info["user_id"]}:{config.get("configurable").get("thread_id")}"""
    value = json.dumps(
        {
            "route": "/aihome/standardCombinationDetail",
            "query": {"unid": link_id_data["unid"]},
        },
        ensure_ascii=False,
    )
    redis.set(key, value)
    logging.info(f"redis enqueued {value} at {key}")

    if ai_msg.tool_calls and len(ai_msg.tool_calls) > 0:
        tool_call = ai_msg.tool_calls[0]
        args = tool_call.get("args")
        final_response = llm.invoke(messages)
        return {
            "messages": [
                AIMessage(
                    content=final_response.content,
                )
            ],
            "current_step": "查询组合信息",
            "current_step_detail": f"""查询组合：{args.get("portfolio_name") or args.get("portfolio_no")} 的详细数据""",
        }

    return {
        "messages": [ai_msg],
        "current_step": "查询组合信息",
        "current_step_detail": "查询组合数据接口",
    }


def prepare_customer_info(state: PortfolioState, config: RunnableConfig):
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {"messages": [AIMessage(content="I couldn't find a question to answer.")]}

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    history_context = (
        create_history_context(state["conversation_history"], llm, max_history_tokens=1500)
        or "No previous financial discussions."
    )

    formatted_prompt = CUSTOMER_BASIC_INFO_PROMPT.format(user_question=user_question, history_context=history_context)
    tools = [fetch_customer_basic_info]
    llm_with_tools = llm.bind_tools(tools)
    c_info_messages: list[AIMessage | HumanMessage] = [HumanMessage(content=formatted_prompt)]
    c_info_ai_msg: AIMessage = llm_with_tools.invoke(c_info_messages)
    c_info_messages.append(c_info_ai_msg)
    c_info_messages, c_info_card_list = execute_tool_calls(c_info_ai_msg, tools, c_info_messages)

    if len(c_info_card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关客户的具体信息")],
            "current_step": "查询客户基本信息",
            "current_step_detail": "查询客户数据接口",
        }

    formatted_prompt = CUSTOMER_HOLD_INFO_PROMPT.format(user_question=user_question, history_context=history_context)
    tools = [fetch_customer_hold_info]
    llm_with_tools = llm.bind_tools(tools)
    c_hold_messages: list[AIMessage | HumanMessage] = [HumanMessage(content=formatted_prompt)]
    c_hold_ai_msg: AIMessage = llm_with_tools.invoke(c_hold_messages)
    c_hold_messages.append(c_hold_ai_msg)
    c_hold_messages, c_hold_card_list = execute_tool_calls(c_hold_ai_msg, tools, c_hold_messages)
    c_info_tool_args = c_info_ai_msg.tool_calls[0].get("args")

    return {
        "current_step": "查询客户基本信息、持仓信息",
        "current_step_detail": f"""查询客户名为：{c_info_tool_args.get("customer_name") or c_info_tool_args.get("customer_id")} 的基本信息数据和持仓信息""",
        "customer_basic_info": c_info_card_list[0]["card_data"].dict(),
        "customer_hold_info": (c_hold_card_list if len(c_hold_card_list) == 0 else c_hold_card_list[0]["card_data"]),
    }


def wait_for_user_input(state: PortfolioState, config: RunnableConfig):
    additional_info = interrupt(
        {
            "current_step": state.get("current_step"),
            "current_step_detail": state.get("current_step_detail"),
            "graph_name": "portfolio_advisor",
            "node_name": "wait_for_user_input",
            "message": AIMessage(
                content="我识别到您想给一个已有的客户，推荐一个标准组合。我会根据该客户的已有基本信息、持仓信息等进行分析，并结合标准化组合的信息，为您推荐组合。您可以告诉我更多关于客户的信息，以便于更好的匹配标准化组合。如果没有更多信息，您直接告诉我没有即可。",
                id=uuid4().hex,
            ),
        }
    )
    return {
        "additional_info": additional_info,
    }


def profile_customer_info(state: PortfolioState, config: RunnableConfig):
    customer_basic_info = state.get("customer_basic_info")
    customer_hold_info = state.get("customer_hold_info")

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    formatted_prompt = CUSTOMER_PROFILE_PROMPT.format(
        customer_basic_info=state.get("customer_basic_info"),
        customer_hold_info=state.get("customer_hold_info"),
        additional_info=state.get("additional_info"),
    )
    customer_profile_message = llm.invoke(formatted_prompt)
    customer_profile = customer_profile_message.content

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return {"messages": [AIMessage(content="未能提取用户认证信息")]}

    portfolios = query_stardard_portfolios(
        user_info,
        {
            "riskLevel": state.get("customer_basic_info")["risk_level"],
        },
    )
    structured_llm = llm.with_structured_output(ProductRecommendPitch)
    endorsePitch = structured_llm.invoke(
        ENDORSE_PORTFOLIOS_PROMPT.format(customer_profile=customer_profile, portfolios=portfolios)
    )
    portfolio = endorsePitch.product
    if (
        portfolio is not None
        and "portfolio_id" in portfolio
        and "first_adjust_date" in portfolio
        and "last_adjust_date" in portfolio
    ):
        link_id_data = generate_portfolio_info_link_id(user_info, portfolio)
        key = f"""polling:{user_info["user_id"]}:{config.get("configurable").get("thread_id")}"""
        value = json.dumps(
            {
                "route": "/aihome/standardCombinationDetail",
                "query": {"unid": link_id_data["unid"]},
            },
            ensure_ascii=False,
        )
        redis.set(key, value)
        logging.info(f"redis enqueued {value} at {key}")

    return {
        "messages": [
            AIMessage(
                content=f"""根据客户{state.get("customer_basic_info")["customer_name"]}的基本信息、持仓信息和本次补充信息，我对客户进行了客户画像分析。""",
                tool_calls=[
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_customer_profile",
                        args={
                            "card_id": "customer_profile",
                            "card_data": {
                                "title": "画像分析",
                                "text": customer_profile,
                            },
                        },
                    ),
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_information_provenance",
                        args={
                            "card_id": "information_provenance",
                            "card_data": {
                                "title": "信息溯源",
                                "table": [
                                    {"name": "基本信息", "data": customer_basic_info},
                                    {"name": "持仓信息", "data": customer_hold_info},
                                ],
                            },
                        },
                    ),
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_portfolio_recommend_pitch",
                        args={
                            "card_id": "portfolio_recommend_pitch",
                            "card_data": {
                                "copyable": False,
                                "text": endorsePitch.reasoning,
                            },
                        },
                    ),
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_portfolio_recommend_pitch",
                        args={
                            "card_id": "portfolio_recommend_pitch",
                            "card_data": {
                                "copyable": True,
                                "text": endorsePitch.pitch,
                            },
                        },
                    ),
                ],
            )
        ],
        "additional_info": None,
        "current_step": "客户画像分析",
        "current_step_detail": "根据客户基本信息和持仓信息生成客户画像",
    }


def portfolio_graph():
    """Create a graph for standard portfolio-related queries."""
    portfolio_graph = StateGraph(PortfolioState)

    portfolio_graph.add_node("portfolio_route_query", portfolio_route_query)
    portfolio_graph.add_node("analyze_portfolio", analyze_portfolio)
    portfolio_graph.add_node("prepare_customer_info", prepare_customer_info)
    portfolio_graph.add_node("wait_for_user_input", wait_for_user_input)
    portfolio_graph.add_node("profile_customer_info", profile_customer_info)

    portfolio_graph.set_entry_point("portfolio_route_query")
    portfolio_graph.add_conditional_edges(
        "portfolio_route_query",
        lambda state: state.get("portfolio_query_type"),
        {
            "analyze_portfolio": "analyze_portfolio",
            "endorse_portfolio_for_customer": "prepare_customer_info",
        },
    )
    portfolio_graph.add_edge("prepare_customer_info", "wait_for_user_input")
    portfolio_graph.add_edge("wait_for_user_input", "profile_customer_info")
    portfolio_graph.add_edge("profile_customer_info", END)
    portfolio_graph.add_edge("analyze_portfolio", END)

    return portfolio_graph.compile(name="portfolio_advisor")
