import logging

from langchain_core.messages import AIMessage, HumanMessage, Tool<PERSON>all
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.types import Command
from typing import Any
from uuid import uuid4

from src.financial_advisor.configuration import Configuration
from src.financial_advisor.models import ProductRecommendPitch, ProductRouteDecision
from src.financial_advisor.prompts import (
    COMPARE_PRODUCTS_PROMPT,
    CUSTOMER_BASIC_INFO_PROMPT,
    CUSTOMER_PRODUCT_RECOMMEND_PITCH_PROMPT,
    PRODUCT_BASIC_INFO_PROMPT,
    PRODUCT_RECOMMEND_PITCH_PROMPT,
    PRODUCT_ROUTING_PROMPT,
    SIMILAR_PRODUCTS_PROMPT,
)
from src.financial_advisor.state import State
from src.financial_advisor.tools.customer_tools import (
    fetch_customer_basic_info,
)
from src.financial_advisor.tools.product_tools import (
    fetch_product_basic_info,
    fetch_product_comparison,
    fetch_product_similarity,
)
from src.utils.history_summariser import (
    add_to_state,
    create_history_context,
    get_latest_user_message,
)
from src.utils.llm_utils import create_llm, execute_tool_calls


logging.basicConfig(level=logging.DEBUG)


def product_route_query(
    state: State, config: RunnableConfig
) -> dict[str, Any] | Command[str]:
    state = add_to_state(state)
    latest_user_message = get_latest_user_message(state.get("messages"))

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "router_model", temperature=0.3)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1000
        )
        or "No previous conversation"
    )
    structured_llm = llm.with_structured_output(ProductRouteDecision)

    try:
        decision = structured_llm.invoke(
            PRODUCT_ROUTING_PROMPT.format(
                query=latest_user_message, history_context=history_context
            )
        )
        product_query_type = decision.route

        logging.info(f"Query: '{latest_user_message[:100]}...'")
        logging.info(
            f"Routed to: {product_query_type} (confidence: {decision.confidence})"
        )
        logging.info(f"Reasoning: {decision.reasoning}")

        # Validate route name
        valid_routes = [
            "product_basic_info",
            "compare_products",
            "similar_products",
            "similar_products_for_customer",
        ]
        if product_query_type not in valid_routes:
            logging.warning(
                f"Invalid route '{product_query_type}', defaulting to general_discussion"
            )
            return Command(goto="general_discussion", graph=Command.PARENT)

    except Exception as e:
        logging.error(
            f"Error in structured LLM routing: {e}, falling back to general_discussion"
        )
        return Command(goto="general_discussion", graph=Command.PARENT)

    return {"product_query_type": product_query_type}


def product_basic_info(state: State, config: RunnableConfig):
    """Fetch product basic information."""
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)

    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )
    formatted_prompt = PRODUCT_BASIC_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )

    tools = [fetch_product_basic_info]
    llm_with_tools = llm.bind_tools(tools)

    messages: list[AIMessage | HumanMessage] = [HumanMessage(content=formatted_prompt)]
    ai_msg: AIMessage = llm_with_tools.invoke(messages)
    messages.append(ai_msg)
    # Execute tool calls if any
    messages, card_list = execute_tool_calls(ai_msg, tools, messages)

    if len(card_list) == 0:
        return {
            **state,
            "current_step": "查询信托产品基本信息",
            "current_step_detail": "未查询到相关产品信息，尝试召回知识库",
            "next_node": "dataset",
            "next_state": state,
        }

    # Get final response if tools were called
    if ai_msg.tool_calls:
        tool_call = ai_msg.tool_calls[0]
        args = tool_call.get("args")
        final_response = llm.invoke(messages)
        return {
            "messages": [
                AIMessage(
                    content=final_response.content,
                    tool_calls=(
                        [
                            ToolCall(
                                id=tool_call.get("id"),
                                name=tool_call.get("name"),
                                args=card_list[0],
                            )
                        ]
                    ),
                )
            ],
            "current_step": "查询信托产品基本信息",
            "current_step_detail": f"""查询信托产品：{args.get("product_name") or args.get("product_code")} 的基本信息数据""",
        }

    return {
        "messages": [ai_msg],
        "current_step": "查询信托产品基础信息",
        "current_step_detail": "查询信托产品数据接口",
    }


def compare_products(state: State, config: RunnableConfig):
    """Fetch product comparison information."""
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)

    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )
    formatted_prompt = COMPARE_PRODUCTS_PROMPT.format(
        user_question=user_question, history_context=history_context
    )

    tools = [fetch_product_comparison]
    llm_with_tools = llm.bind_tools(tools)

    messages: list[AIMessage | HumanMessage] = [HumanMessage(content=formatted_prompt)]
    ai_msg: AIMessage = llm_with_tools.invoke(messages)
    messages.append(ai_msg)
    # Execute tool calls if any
    messages, card_list = execute_tool_calls(ai_msg, tools, messages)

    # Get final response if tools were called
    if ai_msg.tool_calls:
        tool_call = ai_msg.tool_calls[0]
        args = tool_call.get("args")
        final_response = llm.invoke(messages)
        return {
            "messages": [
                AIMessage(
                    content=final_response.content,
                    tool_calls=(
                        [
                            ToolCall(
                                id=tool_call.get("id"),
                                name=tool_call.get("name"),
                                args=card_list[0],
                            )
                        ]
                        if len(card_list) > 0
                        else []
                    ),
                )
            ],
            "current_step": "查询信托产品比较信息",
            "current_step_detail": f"""比较 {",".join(args.get("product_name_list", [])) if len(args.get("product_name_list", [])) > 0 else ",".join(args.get("product_code_list", []))} 的基本信息数据""",
        }

    return {
        "messages": [ai_msg],
        "current_step": "查询信托产品比较信息",
        "current_step_detail": "查询信托产品数据接口",
    }


def similar_products(state: State, config: RunnableConfig):
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )
    total_messages = []

    formatted_prompt = PRODUCT_BASIC_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )
    tools = [fetch_product_basic_info]
    llm_with_tools = llm.bind_tools(tools)
    info_messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=formatted_prompt)
    ]
    info_ai_msg: AIMessage = llm_with_tools.invoke(info_messages)
    info_messages.append(info_ai_msg)
    info_messages, info_card_list = execute_tool_calls(
        info_ai_msg, tools, info_messages
    )
    total_messages.extend(info_messages)

    if len(info_card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关产品的具体信息")],
            "current_step": "查询信托产品相似产品",
            "current_step_detail": "查询信托产品数据接口",
        }
    info_card_data = info_card_list[0]["card_data"]

    formatted_prompt = SIMILAR_PRODUCTS_PROMPT.format(
        user_question=user_question, history_context=history_context
    )
    tools = [fetch_product_similarity]
    llm_with_tools = llm.bind_tools(tools)
    simi_messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=formatted_prompt)
    ]
    simi_ai_msg: AIMessage = llm_with_tools.invoke(simi_messages)
    simi_messages.append(simi_ai_msg)
    simi_messages, simi_card_list = execute_tool_calls(
        simi_ai_msg, tools, simi_messages
    )
    total_messages.extend(simi_messages)

    if len(simi_card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关产品的相似产品")],
            "current_step": "查询信托产品相似产品",
            "current_step_detail": "查询信托产品数据接口",
        }
    simi_card_data = simi_card_list[0]["card_data"]

    formatted_prompt = PRODUCT_RECOMMEND_PITCH_PROMPT.format(
        user_question=user_question,
        current_product=info_card_data,
        similar_products=simi_card_data,
    )
    structured_llm = llm.with_structured_output(ProductRecommendPitch)
    recommendPitch = structured_llm.invoke(formatted_prompt)

    # Get final response if tools were called
    final_response = llm.invoke(total_messages)
    return {
        "messages": [
            AIMessage(
                content=final_response.content,
                tool_calls=(
                    [
                        ToolCall(
                            id=info_ai_msg.tool_calls[0].get("id"),
                            name=info_ai_msg.tool_calls[0].get("name"),
                            args={
                                "card_id": "product_brief_info",
                                "card_data": info_card_data,
                            },
                        ),
                        ToolCall(
                            id=simi_ai_msg.tool_calls[0].get("id"),
                            name=simi_ai_msg.tool_calls[0].get("name"),
                            args={
                                "card_id": simi_card_list[0]["card_id"],
                                "card_data": {
                                    "title": simi_messages[-1].content,
                                    "table": simi_card_data,
                                },
                            },
                        ),
                        ToolCall(
                            id=uuid4().hex,
                            name="fetch_product_recommend_pitch",
                            args={
                                "card_id": "product_recommend_pitch",
                                "card_data": {
                                    "copyable": True,
                                    "text": recommendPitch.pitch,
                                },
                            },
                        ),
                    ]
                ),
            )
        ],
        "current_step": "查询信托产品相似产品",
        "current_step_detail": f"""查询信托产品名称为：{info_ai_msg.tool_calls[0].get("args").get("product_name")} 共{len(simi_card_data)}条相似产品""",
    }


def similar_products_for_customer(state: State, config: RunnableConfig):
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )
    total_messages = []

    formatted_prompt = CUSTOMER_BASIC_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )
    tools = [fetch_customer_basic_info]
    llm_with_tools = llm.bind_tools(tools)
    c_info_messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=formatted_prompt)
    ]
    c_info_ai_msg: AIMessage = llm_with_tools.invoke(c_info_messages)
    c_info_messages.append(c_info_ai_msg)
    c_info_messages, c_info_card_list = execute_tool_calls(
        c_info_ai_msg, tools, c_info_messages
    )
    total_messages.extend(c_info_messages)

    if len(c_info_card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关客户的具体信息")],
            "current_step": "查询客户持仓信托产品相似产品",
            "current_step_detail": "查询客户数据接口",
        }

    formatted_prompt = PRODUCT_BASIC_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )
    tools = [fetch_product_basic_info]
    llm_with_tools = llm.bind_tools(tools)
    info_messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=formatted_prompt)
    ]
    info_ai_msg: AIMessage = llm_with_tools.invoke(info_messages)
    info_messages.append(info_ai_msg)
    info_messages, info_card_list = execute_tool_calls(
        info_ai_msg, tools, info_messages
    )
    total_messages.extend(info_messages)

    if len(info_card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关产品的具体信息")],
            "current_step": "查询信托产品相似产品",
            "current_step_detail": "查询信托产品数据接口",
        }
    info_card_data = info_card_list[0]["card_data"]

    formatted_prompt = SIMILAR_PRODUCTS_PROMPT.format(
        user_question=user_question, history_context=history_context
    )
    tools = [fetch_product_similarity]
    llm_with_tools = llm.bind_tools(tools)
    simi_messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=formatted_prompt)
    ]
    simi_ai_msg: AIMessage = llm_with_tools.invoke(simi_messages)
    simi_messages.append(simi_ai_msg)
    simi_messages, simi_card_list = execute_tool_calls(
        simi_ai_msg, tools, simi_messages
    )
    total_messages.extend(simi_messages)

    if len(simi_card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关产品的相似产品")],
            "current_step": "查询信托产品相似产品",
            "current_step_detail": "查询信托产品数据接口",
        }
    simi_card_data = simi_card_list[0]["card_data"]

    formatted_prompt = CUSTOMER_PRODUCT_RECOMMEND_PITCH_PROMPT.format(
        user_question=user_question,
        customer_basic_info=c_info_card_list[0]["card_data"],
        current_product=info_card_data,
        similar_products=simi_card_data,
    )
    structured_llm = llm.with_structured_output(ProductRecommendPitch)
    recommendPitch = structured_llm.invoke(formatted_prompt)

    # Get final response if tools were called
    final_response = llm.invoke(total_messages)
    return {
        "messages": [
            AIMessage(
                content=final_response.content,
                tool_calls=(
                    [
                        ToolCall(
                            id=info_ai_msg.tool_calls[0].get("id"),
                            name=info_ai_msg.tool_calls[0].get("name"),
                            args={
                                "card_id": "product_brief_info",
                                "card_data": info_card_data,
                            },
                        ),
                        ToolCall(
                            id=simi_ai_msg.tool_calls[0].get("id"),
                            name=simi_ai_msg.tool_calls[0].get("name"),
                            args={
                                "card_id": simi_card_list[0]["card_id"],
                                "card_data": {
                                    "title": simi_messages[-1].content,
                                    "table": simi_card_data,
                                },
                            },
                        ),
                        ToolCall(
                            id=uuid4().hex,
                            name="fetch_product_recommend_pitch",
                            args={
                                "card_id": "product_recommend_pitch",
                                "card_data": {
                                    "copyable": False,
                                    "text": recommendPitch.reasoning,
                                },
                            },
                        ),
                        ToolCall(
                            id=uuid4().hex,
                            name="fetch_product_recommend_pitch",
                            args={
                                "card_id": "product_recommend_pitch",
                                "card_data": {
                                    "copyable": True,
                                    "text": recommendPitch.pitch,
                                },
                            },
                        ),
                    ]
                ),
            )
        ],
        "current_step": "查询信托产品相似产品",
        "current_step_detail": f"""查询信托产品名称为：{info_ai_msg.tool_calls[0].get("args").get("product_name")} 共{len(simi_card_data)}条相似产品""",
    }


def product_graph():
    """Create a graph for product-related queries."""
    product_graph = StateGraph(State)

    product_graph.add_node("product_route_query", product_route_query)
    product_graph.add_node("product_basic_info", product_basic_info)
    product_graph.add_node("compare_products", compare_products)
    product_graph.add_node("similar_products", similar_products)
    product_graph.add_node(
        "similar_products_for_customer", similar_products_for_customer
    )

    product_graph.set_entry_point("product_route_query")
    product_graph.add_conditional_edges(
        "product_route_query",
        lambda state: state.get("product_query_type"),
        {
            "product_basic_info": "product_basic_info",
            "compare_products": "compare_products",
            "similar_products": "similar_products",
            "similar_products_for_customer": "similar_products_for_customer",
        },
    )
    product_graph.add_edge("product_basic_info", END)
    product_graph.add_edge("compare_products", END)
    product_graph.add_edge("similar_products", END)
    product_graph.add_edge("similar_products_for_customer", END)

    return product_graph.compile(name="product_advisor")
