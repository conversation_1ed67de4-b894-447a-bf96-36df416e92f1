import json
import logging
import urllib.parse
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from src.config.load_config import load_config
from src.financial_advisor.api.encrypt import encrypt_aes, decrypt_aes

logger = logging.getLogger(__name__)
REQUEST_AES_KEY = load_config("REQUEST_AES_KEY")


class EncryptionMiddleware(BaseHTTPMiddleware):
    def _process_request_payload(self, request_payload: dict) -> str:
        payload_str = json.dumps(request_payload, ensure_ascii=False, separators=(",", ":"))
        return encrypt_aes(urllib.parse.quote(payload_str), REQUEST_AES_KEY)

    def _process_response_data(self, response_data: str):
        decrypted_data = decrypt_aes(response_data, REQUEST_AES_KEY)
        unquoted_data = urllib.parse.unquote(decrypted_data)
        try:
            return json.loads(unquoted_data)
        except json.JSONDecodeError:
            formatted_data = unquoted_data.replace("\n", "\\n").replace("\r", "\\r")
            return json.loads(formatted_data)

    async def dispatch(self, request: Request, call_next):
        if request.method == "POST" and request.headers.get("content-type") == "application/json":
            try:
                body = await request.json()
                if isinstance(body, dict) and "data" in body:
                    encrypted_body = self._process_response_data(body["data"])
                    request._body = json.dumps(encrypted_body, ensure_ascii=False).encode("utf-8")
            except Exception as e:
                logger.error(f"Request encryption failed: {e}")

        response = await call_next(request)

        print(
            request.url,
            ">>>>>>",
            request.headers.get("accept", "").startswith("application/json"),
            hasattr(response, "body"),
        )

        if request.headers.get("accept", "").startswith("application/json"):
            try:
                if hasattr(response, "body"):
                    body_bytes = response.body if isinstance(response.body, bytes) else response.body.tobytes()
                    encrypted_content = self._process_request_payload(json.loads(body_bytes.decode("utf-8")))
                    return Response(
                        content=encrypted_content,
                        status_code=response.status_code,
                        headers={"content-type": "application/json"},
                    )
            except Exception as e:
                logger.error(f"Response encryption failed: {e}")

        return response
