from pydantic import BaseModel, Field
from typing import Optional, Union


class RouteDecision(BaseModel):
    """Main graph routing decision for user query"""

    route: str = Field(
        description="One of: 'customer_info', 'product_info', 'dataset', 'general_discussion', 'portfolio_info', 'financial_planning'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision in Chinese"
    )


class CustomerRouteDecision(BaseModel):
    """Customer graph routing decision for user query"""

    route: str = Field(
        description="One of: 'customer_basic_info', 'customer_hold_info', 'customer_trade_info'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision"
    )


class ProductRouteDecision(BaseModel):
    """Product graph routing decision for user query"""

    route: str = Field(
        description="One of: 'product_basic_info', 'compare_products', 'similar_products', 'similar_products_for_customer'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision"
    )

class PorfolioRouteDecision(BaseModel):
    """Product graph routing decision for user query"""

    route: str = Field(
        description="One of: 'analyze_portfolio', 'endorse_portfolio_for_customer'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision"
    )

class PlanningRouteDecision(BaseModel):
    """Planning graph routing decision for user query"""

    route: str = Field(
        description="One of: 'planning_for_new_customer', 'planning_for_existing_customer', 'planning_for_existing_customer_with_position'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision"
    )


class MultiProductRecommendPitch(BaseModel):
    """Product recommend pitch for user query"""

    reasoning: str = Field(
        description="Comprehensive explanation for products recommendation"
    )
    pitch: str = Field(description="Products recommendation script for customer")
    products: list = Field(description="Original data of the chosen product list, each product with additional info: weight, weightAmount")


class ProductRecommendPitch(BaseModel):
    """Product recommend pitch for user query"""

    reasoning: str = Field(
        description="Comprehensive explanation for product recommendation"
    )
    pitch: str = Field(description="Product recommendation script for customer")
    product: dict = Field(description="Original data of the chosen product")


class FinancialPlanningOptions(BaseModel):
    """Financial planning options for user query"""

    RANK_CODE: str = Field(description="本次规划期望的组合投资风格: C1-保守型 C2-稳健型 C3-平衡型 C4-成长型 C5-积极型")
    RANK_CODE_REASONING: str = Field(description="选择本次规划期望的组合投资风格选项值的理由")
    CUS_MAX_LOSS_RATE: str = Field(description="可承受最大回撤: 1-最大回撤不超过3% 2-最大回撤不超过5% 3-最大回撤不超过8% 4-最大回撤可以接受超过8%")
    CUS_MAX_LOSS_RATE_REASONING: str = Field(description="选择可承受最大回撤选项值的理由")
    MONETARY_ASSETS: str = Field(description="客户金融资产量: 1-小于100万 2-100(含)~300万 3-300(含)~600万 4-600(含)~1000万 5-1000(含)万以上")
    MONETARY_ASSETS_REASONING: str = Field(description="选择客户金融资产量选项值的理由")
    INVESTMENT_DEADLINE: str = Field(description="客户投资期限: 1-小于1年 2-1(含)~3年 3-3(含)~5年 4-5(含)年以上")
    INVESTMENT_DEADLINE_REASONING: str = Field(description="选择客户投资期限选项值的理由")
    INVESTMENT_AMOUNT: int = Field(description="客户投资金额")
    INVESTMENT_AMOUNT_REASONING: str = Field(description="填写客户投资金额的理由")
    DEMAND_CONTENT: str = Field(description="客户需求描述")
    DEMAND_CONTENT_REASONING: str = Field(description="填写客户需求描述的理由")


class FinancialPlanningProduct(BaseModel):
    proCode: str = Field(description="产品ID")
    proName: str = Field(description="产品名称")
    proShortName: str = Field(description="产品简称")
    assetcatCode: str = Field(description="产品大类代码: DCM-固收类 EQ-权益类 MM-混合类 OTH-商品类及衍生类")
    assetcatName: str = Field(description="产品大类名称")
    bankProCode: str = Field(description="银行间产品代码")
    pfcatCode: str = Field(description="产品分类代码: FUND-公募基金 TRUST-信托产品")
    pfcatName: str = Field(description="产品分类名称")
    proRiskCode: str = Field(description="产品风险等级代码")
    proRiskName: str = Field(description="产品风险等级名称")
    weight: float = Field(description="配置比例(最多保留两位的浮点数,且所有产品比例之和为100%)")
    weightAmount: int = Field(description="配置金额")
    sectorCode: str = Field(description="市场代码")
    sectorName: str = Field(description="市场名称")
    sectorType: str = Field(description="市场类型")
    holdingMoney: Optional[float] = Field(description="持仓金额")
    holdingYn: Optional[str] = Field(description="是否持仓产品: Y-是 N-否")
    productFeature: Optional[str] = Field(description="产品特色")
    profitName: str = Field(description="产品收益能力名称")
    profitValue: Optional[Union[str, int]] = Field(description="产品收益能力")


class FinancialPlanningProducts(BaseModel):
    """Financial planning products for user query"""

    product_allocation: list[FinancialPlanningProduct] = Field(
        description="List of financial planning products"
    )
    product_allocation_reasoning: str = Field(
        description="Comprehensive explanation for the product allocation decision"
    )
