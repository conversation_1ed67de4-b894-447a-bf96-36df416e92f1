import json

from fastapi import APIRouter, Depends
from fastapi.exceptions import HTTPException
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from itertools import groupby
from openai import AsyncOpenAI
from typing import Annotated

from src.config.load_config import load_config
from src.financial_advisor.api.portfolio import (
    async_download_portfolio_pdf,
    async_generate_planning_info_link_id,
    async_query_wealth_planning,
)
from src.financial_advisor.api.product import (
    async_query_allocation_products,
    async_query_indexes,
    async_query_net_value_trend,
)
from src.financial_advisor.dtos import (
    AssetAllocationProductDto,
    AssetAllocationProductNetsDto,
    AssetAllocationProductReplaceDto,
    CreateAssetAllocationDto,
    CreatePlanningInfoLinkDto,
    PaginationDto,
)
from src.financial_advisor.models import MultiProductRecommendPitch, ProductRecommendPitch
from src.financial_advisor.prompts import (
    ASSET_ALLOCATION_PRODUCT_PROMPT,
    PRODUCT_RECOMMEND_PITCH_PROMPT,
)
from src.utils.auth_utils import verify_token

asset_allocation_router = APIRouter(prefix="/asset_allocation")
config = load_config(keys=["OPENAI_API_KEY", "OPENAI_BASE_URL", "OPENAI_MODEL"])
security = HTTPBearer()


PROD_CATE_CODES = ["FUND", "TRUST"]


@asset_allocation_router.post("/ai/products")
async def create_asset_allocation(
    create_asset_allocation_dto: CreateAssetAllocationDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    content = ""
    assets = create_asset_allocation_dto.get("assets")
    match_pool_list = create_asset_allocation_dto.get("match_pool_list")
    planning_type = create_asset_allocation_dto.get("planning_type")
    products = create_asset_allocation_dto.get("products")
    risk_code = create_asset_allocation_dto.get("risk_code")

    for index, asset in enumerate(assets):
        assetcatCode = asset.get("assetcatCode")
        assetCatWeights = asset.get("assetCatWeights")
        assetCatWeightsAmount = asset.get("assetCatWeightsAmount")

        product_list = []
        for pfcatCode in PROD_CATE_CODES:
            prods = await async_query_allocation_products(
                user,
                {
                    "pageNum": 1,
                    "pageSize": 5,
                    "assetcatCode": assetcatCode,
                    "poolIdList": match_pool_list,
                    "pfcatCode": pfcatCode,
                    "riskCode": risk_code,
                },
            )
            product_list.extend(prods.get("apqList") or [])

        openai_client = AsyncOpenAI(api_key=config["OPENAI_API_KEY"], base_url=config["OPENAI_BASE_URL"])
        formatted_prompt = ASSET_ALLOCATION_PRODUCT_PROMPT.format(
            total_amount=assetCatWeightsAmount,
            total_ratio=assetCatWeights,
            products=product_list,
        )
        resp = await openai_client.chat.completions.parse(
            model=config["OPENAI_MODEL"],
            messages=[{"role": "system", "content": formatted_prompt}],
            temperature=0.3,
            response_format=MultiProductRecommendPitch,
        )
        await openai_client.close()
        data = resp.choices[0].message.parsed
        if data is None:
            continue

        grouped = groupby(data.products, key=lambda prod: f"""{prod["pfcatCode"]+"-"+prod["pdcatName"]}""")
        second_product_list = []
        for key, group in grouped:
            second_product_list.append(
                {
                    "pfcatCode": key.split("-")[0],
                    "pdcatName": key.split("-")[1],
                    "products": group,
                }
            )

        assets[index]["secondProdList"] = second_product_list
        content += f"{data.reasoning}\n"

    return {
        "message": "Asset allocation endpoint",
        "data": {
            "assets": assets,
            "reasoning": content,
        },
    }


@asset_allocation_router.post("/indexes")
async def fetch_asset_allocation_indexes(
    asset_allocation_index_dto: PaginationDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    data = await async_query_indexes(user, asset_allocation_index_dto)

    return {
        "message": "Asset allocation endpoint",
        "data": data,
    }


@asset_allocation_router.post("/ai/products/replace")
async def fetch_asset_allocation_similar_product(
    similar_product_replacement_dto: AssetAllocationProductReplaceDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    similar_products = await async_query_allocation_products(
        user,
        {
            "pageNum": 1,
            "pageSize": 10,
            "assetcatCode": similar_product_replacement_dto.assetcatCode,
            "poolIdList": similar_product_replacement_dto.pool_id_list,
            "pfcatCode": similar_product_replacement_dto.pfcatCode,
            "riskCode": similar_product_replacement_dto.proRiskCode,
        },
    )
    similar_products_list = similar_products.get("apqList")

    if similar_products_list is None or len(similar_products_list) == 0:
        return {
            "message": "No product recommended",
            "data": similar_product_replacement_dto,
        }

    openai_client = AsyncOpenAI(api_key=config["OPENAI_API_KEY"], base_url=config["OPENAI_BASE_URL"])
    formatted_prompt = PRODUCT_RECOMMEND_PITCH_PROMPT.format(
        user_question="请通过产品特色(productFeature)、产品收益能力(profitName)等多个维度进行分析，帮我推荐一只相似产品，使用当前产品的权重(weight, weightAmount)进行配置",
        current_product=similar_product_replacement_dto,
        similar_products=similar_products_list,
    )
    resp = await openai_client.chat.completions.parse(
        model=config["OPENAI_MODEL"],
        messages=[{"role": "system", "content": formatted_prompt}],
        temperature=0.3,
        response_format=ProductRecommendPitch,
    )
    await openai_client.close()

    data = resp.choices[0].message.parsed
    if data is None:
        return {
            "message": "No product recommended",
            "data": similar_product_replacement_dto,
        }

    return {"message": "Asset allocation endpoint", "data": data}


@asset_allocation_router.post("/products")
async def fetch_asset_allocation_product(
    asset_allocation_product_dto: AssetAllocationProductDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    data = await async_query_allocation_products(
        user,
        {
            "pageNum": asset_allocation_product_dto.get("page_num"),
            "pageSize": asset_allocation_product_dto.get("page_size"),
            "assetcatCode": asset_allocation_product_dto.get("asset_cat_code"),
            "commonQuery": asset_allocation_product_dto.get("common_query"),
            "poolIdList": asset_allocation_product_dto.get("pool_id_list"),
            "pfcatCode": asset_allocation_product_dto.get("pf_cat_code"),
            "riskCode": asset_allocation_product_dto.get("risk_code"),
        },
    )

    return {
        "message": "Asset allocation product fetched successfully",
        "data": data,
    }


@asset_allocation_router.post("/products/nets")
async def fetch_asset_allocation_product_nets(
    asset_allocation_product_nets_dto: AssetAllocationProductNetsDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    data = await async_query_net_value_trend(
        user,
        {
            "productcode": asset_allocation_product_nets_dto.get("product_code"),
            "pfcatCode": asset_allocation_product_nets_dto.get("pf_cat_code"),
            "dateType": asset_allocation_product_nets_dto.get("date_type"),
        },
    )

    return {
        "message": "Asset allocation product nets fetched successfully",
        "data": data,
    }


@asset_allocation_router.post("/planning_link")
async def create_planning_info_link(
    create_planning_info_link_dto: CreatePlanningInfoLinkDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    wealth_plan_list = await async_query_wealth_planning(
        user,
        {
            "cusName": create_planning_info_link_dto.get("cust_name"),
            "proposalType": [create_planning_info_link_dto.get("proposal_type")],
        },
    )
    wealth_plan_list = [
        item for item in wealth_plan_list if item.get("proposalId") == create_planning_info_link_dto.get("proposal_id")
    ]
    if len(wealth_plan_list) == 0:
        raise HTTPException(status_code=404, detail="Not Found")

    wealth_plan = wealth_plan_list[0]
    link_id_data = await async_generate_planning_info_link_id(user, wealth_plan)

    return {
        "message": "Planning info link created successfully",
        "data": link_id_data,
    }


@asset_allocation_router.post("/planning_pdf")
async def create_planning_pdf(
    create_planning_info_link_dto: CreatePlanningInfoLinkDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    wealth_plan_list = await async_query_wealth_planning(
        user,
        {
            "cusName": create_planning_info_link_dto.get("cust_name"),
            "proposalType": [create_planning_info_link_dto.get("proposal_type")],
        },
    )

    wealth_plan_list = [
        item for item in wealth_plan_list if item.get("proposalId") == create_planning_info_link_dto.get("proposal_id")
    ]
    if len(wealth_plan_list) == 0:
        raise HTTPException(status_code=404, detail="Not Found")

    pdf_data = await async_download_portfolio_pdf(
        user,
        {
            "blobId": create_planning_info_link_dto.get("blob_id"),
            "proposalName": create_planning_info_link_dto.get("proposal_name"),
        },
    )

    return {
        "message": "Planning PDF created successfully",
        "data": pdf_data,
    }


__all__ = ["asset_allocation_router"]
