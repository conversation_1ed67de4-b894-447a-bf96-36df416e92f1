from langchain_core.messages import BaseMessage
from langgraph.graph import add_messages
from typing import Any
from typing_extensions import Annotated, TypedDict


def replace_value(oldVal: str, newVal: str) -> str:
    """Reducer that replaces the old value with the new value."""
    return newVal


class State(TypedDict):
    """理财经理智能体的整体状态

    Attributes:
        conversation_history: 会话历史记录
        current_step: 当前执行的步骤
        current_step_detail: 当前执行的步骤详细信息
        messages: 会话中交换的消息列表
        next_node: 下一个节点
        next_state: 下一个状态
    """

    conversation_history: Annotated[list[BaseMessage], add_messages]
    current_step: Annotated[str, replace_value]
    current_step_detail: Annotated[str, replace_value]
    messages: Annotated[list[str], add_messages]
    next_node: str
    next_state: dict[str, Any]


class SearchState(State):
    """搜索状态

    Attributes:
        search_query: 搜索字符串
        search_result: 搜索结果
        search_sources: 搜索源
    """

    search_query: str
    search_result: list[str]
    search_sources: list[dict]


class PortfolioState(State):
    """标准组合状态

    Attributes:
        additional_info: 附加信息
        customer_basic_info: 客户基本信息
        customer_hold_info: 客户持仓组合
    """

    additional_info: str
    customer_basic_info: dict[str, Any]
    customer_hold_info: list[dict[str, Any]]


class PlanningState(PortfolioState):
    """财富规划状态

    Attributes:
        account_type: 账户类型 0-直销财富 1-资产服务信托
        asset_allocation: 资产配置列表
        customer_type: 客户类型 01-老客户 02-新客户
        ending_action: 结束动作
        financial_planning: 财富规划字典
        financial_planning_id: 财富规划字典ID
        match_group_id: 匹配组ID
        match_pool_list: 匹配产品池
        match_port_id: 匹配组合ID
        product_allocation: 产品配置列表
        product_allocation_bm: 产品配置基准
        product_allocation_dt: 产品配置日期
        planning_amount: 规划金额
        planning_pdf_result: 规划PDF结果
        planning_type: 规划类型 1-结合持仓 2-不结合持仓
        planning_port_id: 规划组合ID
        planning_port_cache_id: 规划组合缓存ID
        profile_message: 客户画像信息
        service_types: 信托服务类型
        system_type: 账户系统 app-直销 fts-家族 stp-家庭 new-全新
        trust_service: 信托服务列表
        trust_project: 信托服务项目
    """

    account_type: str
    asset_allocation: list[dict[str, Any]]
    customer_type: str
    ending_action: str
    financial_planning: list[dict[str, Any]]
    financial_planning_id: str
    match_group_id: str
    match_pool_list: list
    match_port_id: str
    planning_amount: int
    planning_type: str
    planning_pdf_result: dict[str, Any]
    product_allocation: list[dict[str, Any]]
    product_allocation_bm: dict
    product_allocation_dt: str
    profile_message: str
    service_types: dict
    system_type: str
    trust_service: list
    total_amount: int
    trust_project: dict
