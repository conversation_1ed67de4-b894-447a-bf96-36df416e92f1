from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from typing import Optional

from src.financial_advisor.api.customer import (
    query_customer_basic_info,
    query_customer_hold_info,
)
from src.financial_advisor.utils import extractAuthUserInfoFromRunnableConfig


@tool
def fetch_customer_hold_info(
    config: RunnableConfig,
    customer_name: Optional[str] = None,
    customer_no: Optional[str] = None,
):
    """查询客户的持仓信息，如：总资产等。

    Args:
        customer_name (str): 客户姓名，提问可能包含客户姓名，没有则为None
        customer_no (str): 客户号，提问可能包含客户号，没有则为None
    """

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return "未能提取用户认证信息", None

    customer_info = query_customer_basic_info(
        user_info,
        customer_name=customer_name,
        customer_no=customer_no,
    )
    if len(customer_info) == 0:
        return f"很抱歉，未能找到客户{customer_name or customer_no}的基础信息。", None
    if len(customer_info) > 1:
        return (
            f"查询到多个客户信息，请提供更具体的客户姓名或客户号以便进一步筛选。当前查询结果包含{len(customer_info)}个客户的信息。",
            None,
        )

    card_data = query_customer_hold_info(user_info)
    if (
        card_data.get("position_info") is None
        or len(card_data.get("position_info", [])) == 0
    ):
        return "很抱歉，未能找到客户的持仓信息。", None

    return "您好！查询到 {}（ID: {}）的持仓信息如下：{}".format(
        customer_info[0].customer_name, customer_info[0].customer_id, card_data
    ), {
        "card_id": "customer_hold_info",
        "card_data": {**card_data, "customer_info": customer_info[0]},
    }
