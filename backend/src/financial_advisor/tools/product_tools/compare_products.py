from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool

from src.financial_advisor.api.product import query_products
from src.financial_advisor.utils import extractAuthUserInfoFromRunnableConfig


@tool
def fetch_product_comparison(
    config: RunnableConfig,
    product_name_list: list[str] = [],
    product_code_list: list[str] = [],
):
    """查询若干支信托产品的基本信息进行比较。

    Args:
        product_name_list (list): 信托产品名称列表，提问可能包含多个信托产品名称，没有则为空列表
        product_code_list (list): 信托产品代码列表，提问可能包含多个信托产品代码，没有则为空列表
    """

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return "未能提取用户认证信息", None

    if (len(product_name_list) + len(product_code_list) > 4):
        return "同时查询产品数量过多，请调整查询条件。", None

    product_list = []
    if len(product_name_list) > 0:
        product_list.extend(
            query_products(current_manager=user_info, product_name=product_name)
            for product_name in product_name_list
        )
    if len(product_code_list) > 0:
        product_list.extend(
            query_products(
                current_manager=user_info, product_code=",".join(product_code_list)
            )
        )

    if len(product_list) == 0:
        return (
            f"很抱歉，未能找到产品{product_name_list[0] or product_code_list[0]}的基础信息。",
            None,
        )
    if len(product_list) == 1:
        return (
            f"查询到单个产品名称为{product_list[0].product_name}的信息，请提供多个产品名称或产品代码进行比较。",
            None,
        )

    product_list = [p[0] for p in product_list if type(p) == list]

    return (
        f"""您好，以下为产品对比信息：""" + "\n".join([str(p) for p in product_list]),
        {
            "card_id": "compare_products",
            "card_data": product_list,
        },
    )
