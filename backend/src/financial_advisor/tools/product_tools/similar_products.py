from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from typing import Optional

from src.financial_advisor.api.product import query_products
from src.financial_advisor.utils import extractAuthUserInfoFromRunnableConfig


@tool
def fetch_product_similarity(
    config: RunnableConfig,
    product_name: Optional[str] = None,
    product_code: Optional[str] = None,
):
    """查询信托产品的相似产品。

    Args:
        product_name (str): 信托产品名称，提问可能包含信托产品名称，没有则为None
        product_code (str): 信托产品代码，提问可能包含信托产品代码，没有则为None
    """

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return "未能提取用户认证信息", None

    product_info = query_products(
        user_info,
        product_name=product_name,
        product_code=product_code,
    )

    if len(product_info) == 0:
        return f"很抱歉，未能找到产品{product_name or product_code}的基础信息。", None
    if len(product_info) > 1:
        return (
            f"查询到多个产品信息，请提供更具体的产品名称或产品代码以便进一步筛选。当前查询结果包含{len(product_info)}个产品的信息。",
            None,
        )

    products = query_products(user_info)
    similar_products = [
        product
        for product in products
        if product.product_id != product_info[0].product_id
        and product.project_type == product_info[0].project_type
        and product.risk_level == product_info[0].risk_level
    ]

    if len(similar_products) == 0:
        return f"很抱歉，未能找到产品{product_name or product_code}的相似产品。", None

    return "您好，以下是{}的相似产品：".format(product_name or product_code), {
        "card_id": "similar_products",
        "card_data": similar_products,
    }
