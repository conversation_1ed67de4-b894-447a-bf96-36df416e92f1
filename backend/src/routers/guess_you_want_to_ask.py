from fastapi import APIRouter, Depends
from fastapi.exceptions import HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from typing import Annotated

from src.financial_advisor.api.customer import async_query_customer_basic_info
from src.financial_advisor.api.product import async_query_products
from src.utils.auth_utils import verify_token
from src.utils.conversation_utils import generate_similar_question


guess_you_want_to_ask_router = APIRouter(prefix="/guess_you_want_to_ask")
security = HTTPBearer()


@guess_you_want_to_ask_router.get("")
async def guess_you_want_to_ask(
    thread_id: str,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        data = await generate_similar_question(thread_id=thread_id, access_token=token.credentials)
        return {"message": "User question generated successfully", "data": data}
    except Exception as e:
        return {"message": "Error generating user question", "error": str(e)}


__all__ = ["guess_you_want_to_ask_router"]


@guess_you_want_to_ask_router.get("/hook")
async def guess_you_want_to_ask_hook(
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        try:
            user_info = await verify_token(token.credentials)
        except Exception as e:
            raise HTTPException(status_code=401, detail="Unauthorized")

        customers = await async_query_customer_basic_info(user_info)
        customer_name = customers[0].customer_name
        customer_id = customers[0].customer_id

        products = await async_query_products(user_info)
        product_name_01 = products[0].product_name
        product_name_02 = products[1].product_name

        data = [
            {
                "category": "智能问答",
                "scene": "查询客户信息",
                "desc": "问 基本信息资料、持仓信息、交易记录、资产情况",
                "question": f"客户{customer_name}的基本情况是什么？",
            },
            {
                "category": "智能问答",
                "scene": "查询客户信息",
                "desc": "问 基本信息资料、持仓信息、交易记录、资产情况",
                "question": f"客户{customer_name}的风险测评结果如何？",
            },
            {
                "category": "智能问答",
                "scene": "查询产品信息",
                "desc": "问 基本信息资料、持仓信息、交易记录、资产情况",
                "question": f"客户{customer_id}目前持有哪些产品？",
            },
            {
                "category": "智能问答",
                "scene": "查询信托产品",
                "desc": "问 基本信息资料、持仓信息、交易记录、资产情况",
                "question": f"{product_name_01}的起投金额是多少？风险等级和业绩比较基准分别是什么？",
            },
            {
                "category": "智能问答",
                "scene": "对比信托产品",
                "question": f"{product_name_01}和{product_name_02}这两个产品有什么区别？",
            },
            {
                "category": "财富规划",
                "question": "有个新客户叫**，帮我做个新的规划方案，他大概30岁左右，手里有很充裕的现金流动性，对于投资有自己的见解，高知人群，希望一些稳定低波动的产品。",
            },
            {
                "category": "财富规划",
                "question": "有个新客户叫**，帮我做个新的规划方案，他大概30岁左右，手里有很充裕的现金流动性，对于投资有自己的见解，高知人群，希望一些稳定低波动的产品。",
            },
            {"category": "产品推荐", "question": f"帮我推荐一下{product_name_01}的相似产品，并且生成一段话术"},
            {
                "category": "产品推荐",
                "question": f"我有个老客户，客户号是**，他的持仓中有个{product_name_01}，帮我推荐相似产品替换当前产品",
            },
            {"category": "标准组合", "scene": "组合分析", "question": "分析标准化组合**"},
            {"category": "标准组合", "scene": "组合分析", "question": "**标准化组合的大类配置情况怎么样"},
            {
                "category": "标准组合",
                "scene": "组合推荐",
                "question": f"我有一个用户{customer_name}，客户号是{customer_id}，给他推荐标准化组合并生成文案",
            },
        ]
        return {"message": "User question generated successfully", "data": data}
    except Exception as e:
        return {"message": "Error generating user question", "error": str(e)}
