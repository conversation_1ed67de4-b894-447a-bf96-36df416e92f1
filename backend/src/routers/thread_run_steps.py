from fastapi import <PERSON><PERSON>out<PERSON>, Depends
from fastapi.exceptions import HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from typing import Annotated, Any

from src.dtos.thread_run_steps import CreateThreadRunStepsDto
from src.financial_advisor.dtos import ThreadIdDto
from src.utils.auth_utils import verify_token
from src.utils.conversation_utils import query_thread_run_steps, save_thread_run_steps


thread_run_steps_router = APIRouter(prefix="/thread_run_steps")
security = HTTPBearer()


@thread_run_steps_router.post("/list")
async def fetch_thread_run_steps(
    thread_id_dto: ThreadIdDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    try:
        data = await query_thread_run_steps(
            user_id=user["user_id"],
            thread_id=thread_id_dto["thread_id"],
        )
        return {"message": "Thread run steps fetched successfully", "data": data}
    except Exception as e:
        return {"message": "Error fetching thread run steps", "error": str(e)}


@thread_run_steps_router.post("")
async def create_thread_run_steps(
    createThreadRunStepsDto: CreateThreadRunStepsDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    try:
        await save_thread_run_steps(
            user_id=user["user_id"],
            thread_id=createThreadRunStepsDto["thread_id"],
            run_id=createThreadRunStepsDto["run_id"],
            steps=createThreadRunStepsDto["steps"],
        )
        return {"message": "Thread run steps saved successfully"}
    except Exception as e:
        return {"message": "Error saving thread run steps", "error": str(e)}


__all__ = ["thread_run_steps_router"]
