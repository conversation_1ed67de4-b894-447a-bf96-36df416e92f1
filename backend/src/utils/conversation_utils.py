import aiohttp

from openai import AsyncOpenAI
from pydantic import BaseModel, Field

from src.config.load_config import load_config
from src.db.mysql import insert_row, select_row

config = load_config(keys=["OPENAI_API_KEY", "OPENAI_BASE_URL", "OPENAI_MODEL"])


class SimilarQuestion(BaseModel):
    questions: list[str] = Field(description="Similar questions")


GUESS_USER_QUESTION_PROMPT = """You are an AI assistant tasked with predicting the user's next question based on the conversation history. Your goal is to generate 3 potential questions that will guide the user to continue the conversation. When generating these questions, adhere to the following rules:

conversation history: {messages}

1. Use the same language as the user's last question in the conversation history.
2. Keep each question under 20 characters in length.

Analyze the conversation history provided to you and use it as context to generate relevant and engaging follow-up questions. Your predictions should be logical extensions of the current topic or related areas that the user might be interested in exploring further.

Remember to maintain consistency in tone and style with the existing conversation while providing diverse options for the user to choose from. Your goal is to keep the conversation flowing naturally and help the user delve deeper into the subject matter or explore related topics.

Please strictly follow the format rules: 
Return questions in JSON format: ['Question 1', 'Question 2', 'Question 3']. 
请使用中文回答
"""


async def generate_similar_question(thread_id: str, access_token: str):
    async with aiohttp.ClientSession() as session:
        async with session.post(
            url=f"http://127.0.0.1:2024/threads/{thread_id}/history",
            headers={
                "Content-type": "application/json",
                "Authorization": f"Bearer {access_token}",
            },
            json={"limit": 1},
        ) as response:
            [latest] = await response.json()
            if latest is None:
                return []
            messages = latest.get("values").get("messages")
            if len(messages) == 0:
                return []
            openai_client = AsyncOpenAI(
                api_key=config["OPENAI_API_KEY"], base_url=config["OPENAI_BASE_URL"]
            )
            resp = await openai_client.chat.completions.parse(
                model=config["OPENAI_MODEL"],
                messages=[
                    {
                        "role": "system",
                        "content": GUESS_USER_QUESTION_PROMPT.format(messages=messages),
                    }
                ],
                temperature=0.5,
                response_format=SimilarQuestion,
            )
            await openai_client.close()
            await session.close()

            data = resp.choices[0].message.parsed
            return data.questions if data is not None else []


async def query_thread_run_steps(user_id: str, thread_id: str) -> dict | None:
    return await select_row(
        "SELECT * FROM thread_run_steps WHERE user_id = %s AND thread_id = %s",
        (user_id, thread_id),
    )


async def save_thread_run_steps(
    user_id: str, thread_id: str, run_id: str, steps: str
) -> None:
    await insert_row(
        "INSERT INTO thread_run_steps (user_id, thread_id, run_id, steps) VALUES (%s, %s, %s, %s)",
        (user_id, thread_id, run_id, steps),
    )


__all__ = [
    "generate_similar_question",
    "query_thread_run_steps",
    "save_thread_run_steps",
]
