from typing import Any


def sanitize_args(args: Any) -> str:
    """
    Sanitize tool call arguments to prevent special character issues.

    Args:
        args: Tool call arguments string

    Returns:
        str: Sanitized arguments string
    """
    if not isinstance(args, str):
        return ""
    else:
        return args.replace("[", "&#91;").replace("]", "&#93;").replace("{", "&#123;").replace("}", "&#125;")
