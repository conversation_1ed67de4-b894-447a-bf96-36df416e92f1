import logging
import os

from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
from langchain_litellm import ChatLiteLLM
from langchain_openai import ChatOpenAI
from typing import List, Optional


logging.basicConfig(level=logging.DEBUG)


def get_latest_user_message(messages: list) -> Optional[str]:
    """Extract the most recent user message from the message list.

    Args:
        messages: List of messages in either dict or LangChain message format

    Returns:
        The content of the latest user message, or None if not found
    """
    for msg in reversed(messages):
        # Handle dict format
        if isinstance(msg, dict) and msg.get("role") == "user":
            return msg.get("content", "")
        # Handle LangChain HumanMessage objects
        elif isinstance(msg, HumanMessage):
            return msg.content
        # Handle other message objects with role attribute
        elif hasattr(msg, "role") and hasattr(msg, "content") and msg.role == "user":
            return msg.content
    return None


def create_llm(
    configurable,
    model_type: str = "query_generator_model",
    temperature: float = 0.5,
    **kwargs,
) -> ChatOpenAI:
    """Create a standardized LLM instance.

    Args:
        config: Configuration for the runnable
        model_type: The model type key from Configuration
        temperature: Temperature setting for the model
        **kwargs: Additional parameters for ChatOpenAI

    Returns:
        Configured ChatOpenAI instance
    """
    model_name = getattr(configurable, model_type, configurable.query_generator_model)

    default_params = {
        "model": model_name,
        "temperature": temperature,
        "max_retries": 2,
        "api_key": os.getenv("OPENAI_API_KEY"),
        # "api_base": os.getenv("OPENAI_BASE_URL")
        "base_url": os.getenv("OPENAI_BASE_URL"),
        "extra_body": {"disable_search": True}  # Disables <think> tags
    }
    default_params.update(kwargs)

    return ChatOpenAI(**default_params)


def execute_tool_calls(ai_msg: AIMessage, tools: List, messages: List):
    """Execute tool calls from an AI message and return updated messages.

    Args:
        ai_msg: AI message potentially containing tool calls
        tools: List of available tools
        messages: Current message list

    Returns:
        Updated message list with tool results
    """
    if not ai_msg.tool_calls:
        return messages, []

    card_list = []
    for tool_call in ai_msg.tool_calls:
        tool_name = tool_call["name"]
        tool_args = tool_call["args"]

        # Find and execute the tool
        tool_found = False
        for tool in tools:
            if tool.name == tool_name:
                tool_found = True
                try:
                    result, card = tool.invoke(tool_args)
                    logging.info(f"Executing tool {tool_name} with args {tool_args}, and result is {result}")
                    logging.info(f"tool card is {card}")
                    if card is not None:
                        card_list.append(card)
                    messages.append(
                        ToolMessage(content=str(result), tool_call_id=tool_call["id"])
                    )
                except Exception as e:
                    logging.error(f"Error executing tool {tool_name}: {e}")
                    messages.append(
                        ToolMessage(
                            content=f"Error: {str(e)}", tool_call_id=tool_call["id"]
                        )
                    )
                break

        if not tool_found:
            logging.warning(f"Tool {tool_name} not found")
            messages.append(
                ToolMessage(
                    content=f"Error: Tool {tool_name} not found",
                    tool_call_id=tool_call["id"],
                )
            )

    return messages, card_list
