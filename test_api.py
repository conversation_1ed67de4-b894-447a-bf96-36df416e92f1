#!/usr/bin/env python3
"""
测试新的API接口
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_create_thread():
    """测试创建会话"""
    print("=== 测试创建会话 ===")
    url = f"{BASE_URL}/threads"
    data = {"metadata": {"graph_id": "financial_advisor"}}
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"创建的线程ID: {result['thread_id']}")
        print(f"创建时间: {result['created_at']}")
        return result['thread_id']
    else:
        print(f"错误: {response.text}")
        return None

def test_stream_run(thread_id):
    """测试流式对话"""
    print(f"\n=== 测试流式对话 (Thread: {thread_id}) ===")
    url = f"{BASE_URL}/threads/{thread_id}/runs/stream"
    data = {
        "input": {
            "messages": [
                {
                    "id": "test-msg-1",
                    "type": "human",
                    "content": "你好，请简单介绍一下你的功能"
                }
            ]
        },
        "stream_mode": ["values", "messages-tuple", "updates", "custom"],
        "assistant_id": "financial_advisor"
    }
    
    try:
        response = requests.post(url, json=data, stream=True, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("流式响应 (前10条消息):")
            count = 0
            for line in response.iter_lines():
                if line and count < 10:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith('data: '):
                        print(f"  {decoded_line[:100]}...")
                    count += 1
                elif count >= 10:
                    break
            print("  ... (更多消息)")
        else:
            print(f"错误: {response.text}")
    except requests.exceptions.Timeout:
        print("请求超时 (这是正常的，因为流式响应可能很长)")

def test_search_threads():
    """测试搜索会话"""
    print("\n=== 测试搜索会话 ===")
    url = f"{BASE_URL}/threads/search"
    data = {
        "metadata": {"graph_id": "financial_advisor"},
        "limit": 100,
        "offset": 0
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"找到 {len(result)} 个会话")
    else:
        print(f"错误: {response.text}")

def test_thread_history(thread_id):
    """测试获取会话历史"""
    print(f"\n=== 测试获取会话历史 (Thread: {thread_id}) ===")
    url = f"{BASE_URL}/threads/{thread_id}/history"
    data = {"limit": 1000}
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"找到 {len(result)} 条历史记录")
        if result:
            print("第一条记录的部分信息:")
            first_record = result[0]
            print(f"  创建时间: {first_record.get('created_at', 'N/A')}")
            print(f"  Checkpoint ID: {first_record.get('checkpoint_id', 'N/A')}")
            values = first_record.get('values', {})
            messages = values.get('messages', [])
            print(f"  消息数量: {len(messages)}")
    else:
        print(f"错误: {response.text}")

def main():
    print("开始测试新的API接口...")
    
    # 1. 创建会话
    thread_id = test_create_thread()
    if not thread_id:
        print("创建会话失败，停止测试")
        return
    
    # 2. 测试流式对话
    test_stream_run(thread_id)
    
    # 等待一下让流式对话完成
    time.sleep(2)
    
    # 3. 测试搜索会话
    test_search_threads()
    
    # 4. 测试获取会话历史
    test_thread_history(thread_id)
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
